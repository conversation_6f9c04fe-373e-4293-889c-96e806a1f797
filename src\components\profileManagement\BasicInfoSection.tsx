import React from "react";
import { Accordion, Form } from "react-bootstrap";
import { DOBDropdown } from "@/components/common";
import MemoizedRadio from "./MemoizedRadio";

interface BasicInfoSectionProps {
  formik: any;
  handleBlurUncontrolled: (
    field: string
  ) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  seekingForOptions: Array<{ value: any; label: string }>;
  genderOptions: Array<{ value: any; label: string }>;
}

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  formik,
  handleBlurUncontrolled,
  seekingForOptions,
  genderOptions,
}) => (
  <Accordion.Item eventKey="0">
    <Accordion.Header>Basic Information</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>Your Name</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter your name"
            name="name"
            defaultValue={formik.values.name}
            onBlur={handleBlurUncontrolled("name")}
            isInvalid={!!formik.touched.name && !!formik.errors.name}
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors.name}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Date of birth</Form.Label>
          <DOBDropdown
            value={formik.values.dob}
            onChange={(date) => formik.setFieldValue("dob", date)}
            onBlur={() => formik.setFieldTouched("dob", true)}
            error={formik.errors.dob}
            touched={formik.touched.dob}
            maxAge={18}
            customStyling={{
              padding: '12px 30px'
            }}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Username</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter your username"
            name="name"
            defaultValue={formik.values.username}
            isInvalid={!!formik.touched.username && !!formik.errors.username}
            disabled
            className="opacity-75"
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors.name}
          </Form.Control.Feedback>
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Email</Form.Label>
          <Form.Control
            type="email"
            placeholder="Please enter email"
            name="email"
            defaultValue={formik.values.email}
            // onBlur={handleBlurUncontrolled("email")}
            isInvalid={!!formik.touched.email && !!formik.errors.email}
            disabled
            className="opacity-75"
          />
          <Form.Control.Feedback type="invalid">
            {formik.errors.email}
          </Form.Control.Feedback>
        </Form.Group>
        {genderOptions?.length > 0 && (
          <Form.Group className="form-input">
            <Form.Label>Gender</Form.Label>
            <MemoizedRadio
              options={genderOptions}
              value={formik.values.gender}
              // onChange={(e) =>
              //   formik.setFieldValue("gender", Number(e.target.value))
              // }
              name="gender"
              disabled={true}
            />
          </Form.Group>
        )}
        {seekingForOptions?.length > 0 && (
          <Form.Group className="form-input">
            <Form.Label>Seeking For</Form.Label>
            <MemoizedRadio
              options={seekingForOptions}
              value={formik.values.seekingFor}
              // onChange={(e) =>
              //   formik.setFieldValue("seekingFor", Number(e.target.value))
              // }
              name="seekingFor"
              disabled={true}
            />
          </Form.Group>
        )}
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default BasicInfoSection;
