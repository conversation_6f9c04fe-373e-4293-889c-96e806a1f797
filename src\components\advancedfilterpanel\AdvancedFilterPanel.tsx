import React, { useEffect, useState } from "react";
import { Offcanvas, Form, Button } from "react-bootstrap";
import "./styles.scss";

interface AdvancedFilterPanelProps {
  show: boolean;
  onClose: () => void;
  master: any;
  filters: Record<string, any>;
  setFilters: (
    filters:
      | Record<string, any>
      | ((prev: Record<string, any>) => Record<string, any>)
  ) => void;
  setParams: any;
}

const FILTER_CONFIG = [
  {
    label: "Relationship Status",
    filterKey: "relationshipStatusId",
    masterKey: "relationship_status",
  },
  {
    label: "Hair Color",
    filterKey: "hairColorId",
    masterKey: "hair_color",
    color: true,
  },
  {
    label: "Eye Color",
    filterKey: "eyeColorId",
    masterKey: "eye_color",
    color: true,
  },
  {
    label: "Best Feature",
    filterKey: "bestFeatureId",
    masterKey: "best_feature",
  },
  {
    label: "Personality",
    filterKey: "personalityId",
    masterKey: "personality",
  },
  {
    label: "Appearance",
    filterKey: "appearanceId",
    masterKey: "appearance",
  },
  {
    label: "Body Type",
    filterKey: "bodyTypeId",
    masterKey: "body_type",
  },
  {
    label: "Smoking Habit",
    filterKey: "smokingHabitId",
    masterKey: "smoking_habits",
  },
  {
    label: "Drinking Habit",
    filterKey: "drinkingHabitId",
    masterKey: "drinking_habits",
  },
];

const ADVANCED_FILTER_KEYS = FILTER_CONFIG.map((f) => f.filterKey);

const getInitialLocalState = (filters: Record<string, any>) => {
  const state: Record<string, any> = {};
  for (const { filterKey } of FILTER_CONFIG) {
    state[filterKey] = filters[filterKey] ?? undefined;
  }
  return state;
};

const AdvancedFilterPanel: React.FC<AdvancedFilterPanelProps> = ({
  show,
  onClose,
  master,
  filters,
  setFilters,
  setParams,
}) => {
  const [localState, setLocalState] = useState(getInitialLocalState(filters));

  useEffect(() => {
    if (show) {
      setLocalState(getInitialLocalState(filters));
    }
  }, [show, filters]);

  const handleChange = (filterKey: string, value: any) => {
    setLocalState((prev) => ({ ...prev, [filterKey]: value }));
  };

  const handleSubmit = () => {
    // For each advanced filter, store the whole object in global filters
    setFilters((prev: Record<string, any>) => {
      const updated: Record<string, any> = { ...prev };
      FILTER_CONFIG.forEach(({ filterKey }) => {
        updated[filterKey] = localState[filterKey]?.id || localState[filterKey] || undefined;
      });
      return updated;
    });
    
    setParams((prev: Record<string, any>) => {
      const updated: Record<string, any> = { ...prev };
      FILTER_CONFIG.forEach(({ filterKey }) => {
        updated[filterKey] = localState[filterKey]?.id || localState[filterKey] || undefined;
      });
      return updated;
    });
    onClose();
  };

  const handleReset = () => {
    const cleared: Record<string, any> = { ...filters };
    ADVANCED_FILTER_KEYS.forEach((key) => {
      cleared[key] = undefined;
    });
    setFilters(cleared);
    setLocalState(getInitialLocalState(cleared));
  };



  return (
    <Offcanvas
      show={show}
      onHide={onClose}
      placement="end"
      className="advance-filter-offcanvas"
    >
      <Offcanvas.Header closeButton>
        <Offcanvas.Title>Advanced Filters</Offcanvas.Title>
      </Offcanvas.Header>
      <Offcanvas.Body className="filter-sections-container">
        <div className="filter-sections">
          {FILTER_CONFIG.map((section) => {
            const options = master?.[section.masterKey] || [];
            const isColor = !!section.color;
            // Find the selected object for this filterKey
            const selectedObj =
              options.find(
                (opt: any) => localState[section.filterKey] === opt.id
              ) || localState[section.filterKey];
            return (
              <div className="filter-section" key={section.filterKey}>
                <h3 className="filter-section-title">{section.label}</h3>
                <Form.Group className="form-input pb-3">
                  {isColor ? (
                    <div className="filter-options-list d-flex flex-wrap gap-3 hair-color-selector">
                      {options.map((opt: any) => (
                        <Form.Check
                          type="radio"
                          id={`${section.filterKey}-${opt.id}`}
                          name={section.filterKey}
                          key={opt.id}
                          className={`hair-radio ps-0${selectedObj?.id === opt.id ? " selected" : ""} pt-1 ps-1`}
                        >
                          <Form.Check.Input
                            type="radio"
                            id={`${section.filterKey}-${opt.id}`}
                            name={section.filterKey}
                            value={opt.id}
                            checked={selectedObj?.id === opt.id}
                            onChange={() =>
                              handleChange(section.filterKey, opt)
                            }
                            className="d-none"
                          />
                          <Form.Check.Label
                            className="hair-color-box"
                            htmlFor={`${section.filterKey}-${opt.id}`}
                          >
                            <span
                              className="color-outer-ring border-white"
                              style={{ borderColor: opt.colorcode || "#ccc" }}
                            >
                              <span
                                className="color-inner-circle"
                                style={{
                                  backgroundColor: opt.colorcode || "#ccc",
                                }}
                              ></span>
                            </span>
                            <div className="color-label">{opt.title}</div>
                          </Form.Check.Label>
                        </Form.Check>
                      ))}
                    </div>
                  ) : (
                    <div className="filter-options-list d-flex flex-column gap-2">
                      {options.map((opt: any) => (
                        <Form.Check
                          key={opt.id}
                          type="radio"
                          className="mb-1"
                        >
                          <Form.Check.Input
                            type="radio"
                            id={`${section.filterKey}-${opt.id}`}
                            name={section.filterKey}
                            checked={selectedObj?.id === opt.id}
                            onChange={() =>
                              handleChange(section.filterKey, opt)
                            }
                          />
                          <Form.Check.Label
                            htmlFor={`${section.filterKey}-${opt.id}`}
                            style={{ cursor: "pointer" }}
                          >
                            {opt.title}
                          </Form.Check.Label>
                        </Form.Check>
                      ))}
                    </div>
                  )}
                </Form.Group>
              </div>
            );
          })}
        </div>
      </Offcanvas.Body>
      <div className="mt-auto d-flex justify-content-between gap-3 filter-btns">
        <Button
          variant="outline-secondary"
          className="w-50 first-btn"
          onClick={handleReset}
        >
          Clear
        </Button>
        <Button
          variant="primary"
          className="w-50 second-btn"
          onClick={handleSubmit}
        >
          Search
        </Button>
      </div>
    </Offcanvas>
  );
};

export default AdvancedFilterPanel;
