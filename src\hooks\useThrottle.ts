import { useCallback, useRef } from 'react';

/**
 * Custom hook for throttling function calls
 * @param callback - Function to throttle
 * @param delay - Delay in milliseconds
 * @returns Throttled function
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef<number>(Date.now());

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

/**
 * Custom hook for debouncing function calls
 * @param callback - Function to debounce
 * @param delay - Delay in milliseconds
 * @returns Debounced function
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => callback(...args), delay);
    }) as T,
    [callback, delay]
  );
};

/**
 * Custom hook for RAF-based throttling (for scroll events)
 * @param callback - Function to throttle
 * @returns RAF-throttled function
 */
export const useRafThrottle = <T extends (...args: any[]) => any>(
  callback: T
): T => {
  const rafId = useRef<number>();
  const lastArgs = useRef<Parameters<T>>();

  return useCallback(
    ((...args: Parameters<T>) => {
      lastArgs.current = args;
      if (rafId.current) return;

      rafId.current = requestAnimationFrame(() => {
        callback(...(lastArgs.current as Parameters<T>));
        rafId.current = undefined;
      });
    }) as T,
    [callback]
  );
};
