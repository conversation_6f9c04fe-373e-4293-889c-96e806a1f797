import {
  useUpdateAvatarMutation,
  useUploadAlbumMutation,
  useDeleteImageMutation,
} from "@/api/user.api";
import { useS3Upload } from "@/hooks";
import { setConfirmModalConfig } from "@/stores";
import useUserStore, { setUserAvatar } from "@/stores/user";
import { AddCircle, Camera } from "iconsax-react";
import { CloseCircle } from "iconsax-react";
import React, { useEffect, useRef, useState } from "react";
import { Form, Image, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";

interface PhotoItem {
  id: string;
  image: string;
  filename?: string;
}

const MyPhotos = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const userData = useUserStore((state) => state.userInfo.user);
  const [photos, setPhotos] = useState<PhotoItem[]>([]);

  const { uploadFile, isUploading } = useS3Upload();
  const { mutateAsync: uploadAlbum } = useUploadAlbumMutation();
  const { mutateAsync: updateAvatar } = useUpdateAvatarMutation();
  const { mutateAsync: deleteImage } = useDeleteImageMutation();
  const setUserDetails = useUserStore((state) => state.setUserDetails);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    const file = files[0];
    if (!file) return;

    try {
      // Upload single file to S3
      const uploadResult = await uploadFile(file);

      if (!uploadResult.success) {
        toast.error(uploadResult.error || "Failed to upload image");
        return;
      }

      const albumResult: any = await uploadAlbum({
        images: [uploadResult.filename],
      });

      if (albumResult?.success) {
        toast.success("Photo uploaded successfully!");

        // const newPhoto = {
        //   id: `new-${Date.now()}`,
        //   image: uploadResult.filename || "",
        //   filename: uploadResult.filename || "",
        // };
        const newPhoto = albumResult?.data?.image[0];
        setPhotos((prev) => [...prev, newPhoto]);
      } else {
        toast.error("Failed to save photo to album");
      }
    } catch (error: any) {
      console.error("Upload error:", error);
      toast.error(error?.message || "Failed to upload photo");
    }
  };

  const handleSetAsAvatar = (photo: PhotoItem) => {
    if (!photo.image) {
      toast.error("This photo cannot be set as avatar");
      return;
    }

    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: async () => {
          try {
            const result: any = await updateAvatar({
              imagePath: photo.image,
              isAlbum: true,
            });
            if (result?.success && photo.image) {
              const currentUser = useUserStore.getState().userInfo.user;
              setUserDetails({
                ...currentUser,
                avatar: photo.image,
              });
              toast.success("Profile picture updated successfully!");
            }
          } catch (error: any) {
            console.error("Avatar update error:", error);
            toast.error("Failed to update profile picture");
          }
        },
        icon: "ProfileTick",
        iconColor: "#B936AD",
        content: {
          heading: "Profile Picture",
          description:
            "Are you sure you want to set this photo as your profile picture?",
        },
        showCloseIcon: true,
        buttonText: "Set as Profile Picture",
      },
    });
  };

  const handleDeleteImage = (photo: PhotoItem) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: async () => {
          try {
            const result: any = await deleteImage(photo.id);
            if (result?.success) {
              setUserAvatar("");
              setPhotos((prev) => prev.filter((p) => p.id !== photo.id));
              toast.success("Photo deleted successfully!");
            }
          } catch (error: any) {
            toast.error(error?.message || "Failed to delete photo");
          }
        },
        content: {
          heading: "Delete Photo",
          description: "Are you sure you want to delete this photo?",
        },
        showCloseIcon: true,
        buttonText: "Confirm",
      },
    });
  };

  const isCurrentActive = (image: string) => {
    return userData.avatar === image;
  };

  useEffect(() => {
    if (userData?.images?.length) {
      setPhotos(userData?.images);
    }
  }, [userData?.images?.length]);

  return (
    <div className="my-photos d-flex flex-wrap">
      <div className="my-photos-upload position-relative d-flex align-items-center justify-content-center">
        <div className="text-center">
          {isUploading ? (
            <Spinner animation="border" variant="primary" />
          ) : (
            <AddCircle size="55" color="#141414" variant="Bold" />
          )}
          <h6 className="mb-0 mt-3">
            {isUploading ? "Uploading..." : "Upload Photos"}
          </h6>
        </div>
        <Form.Control
          type="file"
          accept="image/jpeg,image/jpg,image/png"
          className="photo-upload"
          onChange={handleFileChange}
          ref={fileInputRef}
          disabled={isUploading}
        />
      </div>

      {photos.map((photo) => (
        <div key={photo.id} className="position-relative">
          <Image
            className="my-photos-img"
            src={`${import.meta.env.VITE_S3_BASE_URL}${photo.image}`}
            alt="user-pic"
          />
          {photo.image && (
            <div className="position-absolute top-0 end-0 m-2 d-flex gap-2">
              {!isCurrentActive(photo.image) && (
                <div
                  className="bg-color-primary rounded-circle d-flex align-items-center justify-content-center"
                  style={{ width: "30px", height: "30px", cursor: "pointer" }}
                  onClick={() => handleSetAsAvatar(photo)}
                  title="Set as profile picture"
                >
                  <Camera size="16" color="#fff" variant="Bold" />
                </div>
              )}
              <div
                className="bg-danger rounded-circle d-flex align-items-center justify-content-center"
                style={{ width: "30px", height: "30px", cursor: "pointer" }}
                onClick={() => handleDeleteImage(photo)}
                title="Delete photo"
              >
                <CloseCircle size="16" color="#fff" variant="Bold" />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default MyPhotos;
