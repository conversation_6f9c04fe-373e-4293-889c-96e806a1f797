import defaultProfile from "@/assets/images/user.png";
import { FlirtInterface } from "@/types";
import { FormatS3ImgUrl } from "@/utils";
import { formatDistanceToNow } from "date-fns";
import { Trash } from "iconsax-react";
import React, { useState } from "react";
import { Card, Image, Placeholder } from "react-bootstrap";
import "./styles.scss";

interface FlirtCardProps extends FlirtInterface {
  onDelete: (flirtId: number) => void;
}

const FlirtCard: React.FC<FlirtCardProps> = ({
  id,
  otherUser,
  message,
  createdAt,
  onDelete,
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleDelete = () => {
    onDelete(id);
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return "Unknown time";
    }
  };

  return (
    <Card className="flirt-card mb-3">
      <Card.Body className="d-flex gap-3 p-3">
        {/* User Avatar */}
        <div className="flex-shrink-0">
          <div className="avatar-container position-relative">
            {!imageLoaded && (
              <Placeholder
                as="div"
                animation="glow"
                className="avatar-placeholder"
              >
                <Placeholder xs={12} style={{ height: "100%" }} />
              </Placeholder>
            )}
            <Image
              src={
                otherUser.avatar
                  ? FormatS3ImgUrl(otherUser.avatar)
                  : defaultProfile
              }
              className={`user-avatar ${imageLoaded ? "loaded" : "loading"}`}
              onLoad={() => setImageLoaded(true)}
              onError={() => setImageLoaded(true)}
            />
          </div>
        </div>

        {/* Flirt Content */}
        <div className="flex-grow-1 d-flex flex-column">
          <div className="d-flex justify-content-between align-items-start mb-2">
            <div>
              <h6 className="username mb-1">{otherUser.username}</h6>
              <small className="text-muted time">{formatTime(createdAt)}</small>
            </div>

            {/* Actions Dropdown */}
            <div>
              <button
                onClick={handleDelete}
                className="text-danger bg-transparent border-0 p-0"
                title="Delete Flirt"
              >
                <Trash size="25" color="#dc3545" />
              </button>
            </div>
          </div>

          {/* Flirt Message */}
          <div className="flirt-message">
            <p className="mb-0">{message}</p>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

// Skeleton component for loading state
export const FlirtCardSkeleton: React.FC = () => {
  return (
    <Card className="flirt-card mb-3">
      <Card.Body className="d-flex gap-3 p-3">
        {/* Avatar Skeleton */}
        <div className="flex-shrink-0">
          <Placeholder
            as="div"
            animation="glow"
            className="user-avatar"
            style={{ borderRadius: "50%" }}
          >
            <Placeholder xs={12} style={{ height: "100%" }} />
          </Placeholder>
        </div>

        {/* Content Skeleton */}
        <div className="flex-grow-1">
          <div className="d-flex justify-content-between align-items-start mb-2">
            <div className="flex-grow-1">
              <Placeholder as="h6" animation="glow" className="mb-1">
                <Placeholder xs={6} />
              </Placeholder>
              <Placeholder as="small" animation="glow">
                <Placeholder xs={4} />
              </Placeholder>
            </div>
            <Placeholder
              animation="glow"
              style={{ width: "20px", height: "20px", borderRadius: "50%" }}
            >
              <Placeholder xs={12} style={{ height: "100%" }} />
            </Placeholder>
          </div>

          <Placeholder as="p" animation="glow" className="mb-0">
            <Placeholder xs={12} />
            <Placeholder xs={8} />
          </Placeholder>
        </div>
      </Card.Body>
    </Card>
  );
};

export default FlirtCard;
