export interface FlirtUser {
  id: number;
  username: string;
  name: string | null;
  avatar: string;
  userType: 'model' | 'user';
}

export interface FlirtInterface {
  id: number;
  type: 'sent' | 'received';
  otherUser: FlirtUser;
  message: string;
  createdAt: string;
}

export interface FlirtResponse {
  success: boolean;
  message: string;
  data: {
    flirts: FlirtInterface[];
    meta: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

export interface FlirtListProps {
  flirts: FlirtInterface[];
  hasNextPage: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  onDeleteFlirt: (flirtId: number) => void;
}
