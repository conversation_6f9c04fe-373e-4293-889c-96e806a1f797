import useChatStore from "@/stores/useChatStore";
import { ModelInterface } from "@/types";
import { calculateAgeByDOB, FormatLocation } from "@/utils";
import { Sms } from "iconsax-react";
import React, { useState } from "react";
import { Button, Placeholder } from "react-bootstrap";
import FlirtPanel from "../flirtpanel/FlirtPanel";
import LazyImage from "../LazyImage";
import FavoriteToggle from "./FavoriteToggle";
import MemberProfilePanel from "./memberProfilePanel";
import "./styles.scss";

const MembersCard: React.FC<ModelInterface> = (modelProps) => {
  const [flirtShow, setFlirtShow] = useState<boolean>(false);
  const [profileShow, setProfileShow] = useState<boolean>(false);
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);
  const { setActiveChatUser } = useChatStore();

  const {
    avatar,
    username,
    city,
    modelId: memberId,
    isFavorite,
    id,
    refetchModels,
    dob,
  } = modelProps || {};

  const handleClose = () => {
    setProfileShow(false);
    setSelectedMemberId(null);
  };

  const handleShow = (e: any) => {
    e.stopPropagation();
    setSelectedMemberId(id);
    setProfileShow(true);
  };
  const handleFlirtShow = (e: any) => {
    e.stopPropagation();
    setSelectedMemberId(id);
    setFlirtShow(true);
  };
  const onHide = () => {
    setFlirtShow(false);
  };

  const handleChatShow = (e: any) => {
    e.stopPropagation();
    setActiveChatUser({
      id: id,
      name: username,
      location: FormatLocation(undefined, city),
      avatar: avatar,
    });
  };

  return (
    <>
      <div className="member-card overflow-hidden" onClick={handleShow}>
        <div className="position-relative w-100">
          {/* <Image
            src={avatar ? FormatS3ImgUrl(avatar) : defaultProfile}
            className="member-img w-100"
            loading="lazy"
          /> */}
          <LazyImage
            src={avatar}
            alt={`${username}'s profile picture`}
            className={`member-img w-100 ${!avatar && "placeholder-image"}`}
            size="thumbnail"
            enableProgressiveLoading={true}
            rootMargin="100px"
          />
          <FavoriteToggle
            modelId={memberId}
            initialFavorite={isFavorite}
            refetchModels={refetchModels}
          />
        </div>
        <div className="p-3 d-flex flex-column gap-3">
          <div className="d-flex justify-content-between gap-3">
            <div className="d-flex flex-column gap-1">
              <h3 className="mb-0 name">
                {username}
                {calculateAgeByDOB(dob)}
              </h3>
              <p className="mb-0 location">{FormatLocation(undefined, city)}</p>
            </div>
            <Button variant="outline-warning" className="email-btn">
              <Sms size="32" color="#f68507" variant="Bold" />
            </Button>
          </div>
          <div className="d-flex gap-2">
            <Button
              variant="outline-warning"
              className="w-100 first-btn"
              onClick={handleChatShow}
            >
              Chat
            </Button>
            <Button
              variant="gradient-purple"
              className="w-100 second-btn"
              onClick={handleFlirtShow}
            >
              Flirt
            </Button>
          </div>
        </div>
      </div>
      {selectedMemberId && (
        <MemberProfilePanel
          show={profileShow}
          onHide={handleClose}
          memberId={selectedMemberId}
          handleFlirtShow={handleFlirtShow}
        />
      )}
      {flirtShow && selectedMemberId && (
        <FlirtPanel
          show={flirtShow}
          onHide={onHide}
          memberId={selectedMemberId}
        />
      )}
    </>
  );
};

export const MembersCardSkeleton: React.FC = () => {
  return (
    <div className="member-card overflow-hidden">
      <div className="position-relative w-100">
        <Placeholder
          as="div"
          animation="glow"
          className="member-img w-100"
          style={{ height: "220px", borderRadius: "8px 8px 0px 0px" }}
        >
          <Placeholder xs={12} style={{ height: "100%" }} />
        </Placeholder>
      </div>
      <div className="p-3 d-flex flex-column gap-3">
        <div className="d-flex justify-content-between gap-3">
          <div className="d-flex flex-column gap-1">
            <Placeholder as="h3" animation="glow" className="mb-0">
              <Placeholder xs={8} />
            </Placeholder>
          </div>
        </div>
        <div className="d-flex gap-2">
          <Placeholder
            as={Button}
            animation="wave"
            className="w-100"
          ></Placeholder>
          <Placeholder as={Button} animation="wave" className="w-100">
            <Placeholder />
          </Placeholder>
        </div>
      </div>
    </div>
  );
};

export default MembersCard;
