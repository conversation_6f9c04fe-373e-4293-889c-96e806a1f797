import React from "react";
import { Form } from "react-bootstrap";
import ReactSelect from "react-select";
import "./styles.scss";

interface DOBDropdownProps {
  value?: string; // Date string in YYYY-MM-DD format
  onChange: (date: string) => void;
  onBlur?: (field: string) => void;
  error?: string;
  touched?: boolean;
  disabled?: boolean;
  maxAge?: number; // Maximum age allowed (default: 18)
  minAge?: number; // Minimum age allowed (default: 100)
  customStyling?: any;
}

const DOBDropdown: React.FC<DOBDropdownProps> = ({
  value = "",
  onChange,
  onBlur,
  error,
  touched,
  disabled = false,
  maxAge = 18,
  minAge = 100,
}) => {
  // Parse the current value
  const parseDate = (dateString: string) => {
    if (!dateString) return { day: "", month: "", year: "" };
    const [year, month, day] = dateString.split("-");
    return {
      day: day || "",
      month: month || "",
      year: year || "",
    };
  };

  const { day, month, year } = parseDate(value);

  // Generate options
  const dayOptions = Array.from({ length: 31 }, (_, i) => ({
    value: String(i + 1).padStart(2, "0"),
    label: String(i + 1),
  }));

  const monthOptions = [
    { value: "01", label: "January" },
    { value: "02", label: "February" },
    { value: "03", label: "March" },
    { value: "04", label: "April" },
    { value: "05", label: "May" },
    { value: "06", label: "June" },
    { value: "07", label: "July" },
    { value: "08", label: "August" },
    { value: "09", label: "September" },
    { value: "10", label: "October" },
    { value: "11", label: "November" },
    { value: "12", label: "December" },
  ];

  const currentYear = new Date().getFullYear();
  const maxYear = currentYear - maxAge;
  const minYear = currentYear - minAge;

  const yearOptions = Array.from({ length: maxYear - minYear + 1 }, (_, i) => {
    const yearValue = maxYear - i;
    return {
      value: String(yearValue),
      label: String(yearValue),
    };
  });

  // Handle changes
  const handleChange = (
    field: "day" | "month" | "year",
    selectedValue: string
  ) => {
    const currentDate = parseDate(value);
    const newDate = { ...currentDate, [field]: selectedValue };

    // Validate the date and construct the date string
    if (newDate.day && newDate.month && newDate.year) {
      // Check if the date is valid
      const dateObj = new Date(
        parseInt(newDate.year),
        parseInt(newDate.month) - 1,
        parseInt(newDate.day)
      );

      // Check if the constructed date matches the input values
      if (
        dateObj.getDate() === parseInt(newDate.day) &&
        dateObj.getMonth() === parseInt(newDate.month) - 1 &&
        dateObj.getFullYear() === parseInt(newDate.year)
      ) {
        onChange(`${newDate.year}-${newDate.month}-${newDate.day}`);
      } else {
        // Invalid date, but still update to show user selection
        onChange(`${newDate.year}-${newDate.month}-${newDate.day}`);
      }
    } else {
      // Partial date, still update
      const partialDate = `${newDate.year || ""}-${newDate.month || ""}-${newDate.day || ""}`;
      onChange(partialDate);
    }
  };

  const handleBlur = () => {
    if (onBlur) {
      onBlur("dob");
    }
  };

  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      borderColor: error && touched ? "#dc3545" : provided.borderColor,
      "&:hover": {
        borderColor: error && touched ? "#dc3545" : provided.borderColor,
      },
      boxShadow: state.isFocused
        ? error && touched
          ? "0 0 0 0.2rem rgba(220, 53, 69, 0.25)"
          : "0 0 0 0.2rem rgba(0, 123, 255, 0.25)"
        : provided.boxShadow,
    }),
  };

  return (
    <div className="dob-dropdown">
      <div className="row g-2">
        <div className="col-4">
          <ReactSelect
            options={dayOptions}
            value={dayOptions.find((option) => option.value === day) || null}
            onChange={(option) => handleChange("day", option?.value || "")}
            onBlur={handleBlur}
            placeholder="DD"
            isSearchable={false}
            isDisabled={disabled}
            classNamePrefix="select"
            styles={customStyles}
          />
        </div>
        <div className="col-4">
          <ReactSelect
            options={monthOptions}
            value={
              monthOptions.find((option) => option.value === month) || null
            }
            onChange={(option) => handleChange("month", option?.value || "")}
            onBlur={handleBlur}
            placeholder="MM"
            isSearchable={false}
            isDisabled={disabled}
            classNamePrefix="select"
            styles={customStyles}
          />
        </div>
        <div className="col-4">
          <ReactSelect
            options={yearOptions}
            value={yearOptions.find((option) => option.value === year) || null}
            onChange={(option) => handleChange("year", option?.value || "")}
            onBlur={handleBlur}
            placeholder="YYYY"
            isSearchable={true}
            isDisabled={disabled}
            classNamePrefix="select"
            styles={customStyles}
          />
        </div>
      </div>
      {touched && error && (
        <Form.Control.Feedback type="invalid" className="d-block text-danger">
          {error}
        </Form.Control.Feedback>
      )}
    </div>
  );
};

export default DOBDropdown;
