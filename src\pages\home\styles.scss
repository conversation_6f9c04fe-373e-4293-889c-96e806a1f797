@use '@/variables' as *;

.banner-img {
    background-image: url('../../assets/images/member-banner.jpg');
    background-size: cover;
    width: 100%;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 200px;
    padding-top: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 1600px) {
       min-height: 170px;
    }

    @media (max-width: 1150px) {
        min-height: 120px;
    }

    .title {
        color: $white-color;
        font-size: 28px;
        font-weight: 700;
        line-height: 1.1;
    }

    .description {
        color: $white-color;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.2;
    }
}

.navbar-search-bar {
    .search-bar-wrapper {
        padding-inline: 80px;
        @media (max-width: 1400px) {
            padding-inline: 50px;
        }
    }
}

.members-list-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(272px, 1fr));
    gap: 20px;
}