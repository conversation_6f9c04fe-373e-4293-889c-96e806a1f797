@use '@/variables' as *;

.banner-img {
    background-image: url('../../assets/images/member-banner.jpg');
    background-size: cover;
    width: 100%;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 200px;
    padding-top: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 1600px) {
       min-height: 170px;
    }

    @media (max-width: 1150px) {
        min-height: 120px;
    }

    .title {
        color: $white-color;
        font-size: 28px;
        font-weight: 700;
        line-height: 1.1;
    }

    .description {
        color: $white-color;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.2;
    }
}

.navbar-search-bar {
    .search-bar-wrapper {
        padding-inline: 80px;
        @media (max-width: 1400px) {
            padding-inline: 50px;
        }
    }
}

.members-list-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(272px, 1fr));
    gap: 20px;

    // Staggered animation for cards
    .member-card {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;

        @for $i from 1 through 20 {
            &:nth-child(#{$i}) {
                animation-delay: #{$i * 0.05}s;
            }
        }
    }
}

// Smooth sticky search bar transition
.navbar-search-bar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.position-fixed {
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.95) !important;
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        animation: slideDown 0.3s ease-out;
    }
}

// Animations
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

// Performance optimizations
.members-list {
    contain: layout style paint;
    will-change: scroll-position;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
    .member-card,
    .navbar-search-bar {
        animation: none !important;
        transition: none !important;
    }
}