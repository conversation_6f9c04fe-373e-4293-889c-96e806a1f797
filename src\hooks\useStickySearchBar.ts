import { useEffect, useState, useRef, useCallback } from 'react';
import { useRafThrottle } from './useThrottle';

interface UseStickySearchBarOptions {
  offset?: number;
  disabled?: boolean;
}

export const useStickySearchBar = (options: UseStickySearchBarOptions = {}) => {
  const { offset = 66, disabled = false } = options;
  const [isSticky, setIsSticky] = useState(false);
  const elementRef = useRef<HTMLDivElement | null>(null);
  const initialOffsetRef = useRef<number | null>(null);

  const handleScroll = useCallback(() => {
    if (!elementRef.current || disabled) return;

    // Get initial offset on first scroll
    if (initialOffsetRef.current === null) {
      const rect = elementRef.current.getBoundingClientRect();
      initialOffsetRef.current = window.scrollY + rect.top;
    }

    const shouldBeSticky = window.scrollY > (initialOffsetRef.current + offset);
    
    if (shouldBeSticky !== isSticky) {
      setIsSticky(shouldBeSticky);
    }
  }, [isSticky, offset, disabled]);

  // Use RAF throttling for smooth performance
  const throttledHandleScroll = useRafThrottle(handleScroll);

  useEffect(() => {
    if (disabled) {
      setIsSticky(false);
      return;
    }

    // Initial check
    handleScroll();

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [throttledHandleScroll, handleScroll, disabled]);

  // Reset when disabled changes
  useEffect(() => {
    if (disabled) {
      initialOffsetRef.current = null;
      setIsSticky(false);
    }
  }, [disabled]);

  return {
    elementRef,
    isSticky,
  };
};
