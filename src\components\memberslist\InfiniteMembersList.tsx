import React, { memo, useEffect } from "react";
import MembersCard, { MembersCardSkeleton } from "../memberscard/MembersCard";
import { ModelInterface } from "@/types";
import { Spinner } from "react-bootstrap";
import { useInfiniteScroll } from "@/hooks";
import { preloadCriticalImages } from "@/utils/imagePreloader";
import { getOptimizedImageUrl, getOptimalImageFormat } from "@/utils/utils";

interface InfiniteMembersListProps {
  models: ModelInterface[];
  hasNextPage: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  refetchModels: () => void;
}

const InfiniteMembersList: React.FC<InfiniteMembersListProps> = memo(
  ({
    models,
    hasNextPage,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    refetchModels,
  }) => {
    const { loadMoreRef } = useInfiniteScroll({
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
      rootMargin: "200px",
    });

    // Preload critical images when models change
    useEffect(() => {
      if (models.length > 0) {
        const imageUrls = models
          .slice(0, 6) // Only preload first 6 images
          .map(model => model.avatar)
          .filter(Boolean)
          .map(avatar => getOptimizedImageUrl(avatar, {
            size: 'thumbnail',
            format: getOptimalImageFormat()
          }));

        if (imageUrls.length > 0) {
          preloadCriticalImages(imageUrls);
        }
      }
    }, [models]);

    if (isLoading && models.length === 0) {
      return (
        <div className="d-flex flex-wrap members-list-content">
          {Array.from({ length: 6 }).map((_, index) => (
            <MembersCardSkeleton key={`skeleton-${index}`} />
          ))}
        </div>
      );
    }

    return (
      <>
        <div className="members-list-content">
          {models.map((model: ModelInterface) => (
            <MembersCard
              key={model.id}
              {...model}
              refetchModels={refetchModels}
            />
          ))}
        </div>

        <div ref={loadMoreRef} style={{ height: "20px" }} />

        {isFetchingNextPage && (
          <div className="d-flex justify-content-center align-items-center w-100 py-4">
            <Spinner animation="border" />
          </div>
        )}
      </>
    );
  }
);

InfiniteMembersList.displayName = "InfiniteMembersList";

export default InfiniteMembersList;
