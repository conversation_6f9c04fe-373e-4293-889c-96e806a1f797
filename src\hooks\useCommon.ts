import { useLogoutMutation } from "@/api";
import { ROUTE_PATH } from "@/routes";
import { resetUserState } from "@/stores";
import { cleanupChatSocket } from "@/stores/useChatStore";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export const useLogout = () => {
  const navigate = useNavigate();
  const { mutateAsync: logout } = useLogoutMutation();

  const onLogout = async () => {
    try {
      await logout();
      // Cleanup socket connection before resetting user state
      cleanupChatSocket();
      resetUserState();
      navigate(ROUTE_PATH.LOGIN);
    } catch (err) {
      console.log(err);
    }
  };

  return onLogout;
};

export const useAutoScroll = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
};

export const useInvalidateQuery = () => {
  const queryClient = useQueryClient();

  const invalidateQueries = async (queryKeys: any) => {
    if (!queryKeys.length) return;

    await queryClient.invalidateQueries({
      predicate: (query) => queryKeys?.includes(query?.queryKey[0] as string),
    });
  };

  return [invalidateQueries];
};
