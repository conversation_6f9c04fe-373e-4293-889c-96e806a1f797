.dob-dropdown {
  .select__control {
    min-height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    
    &:hover {
      border-color: #86b7fe;
    }
    
    &.select__control--is-focused {
      border-color: #86b7fe;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  }
  
  .select__value-container {
    padding: 2px 8px;
  }
  
  .select__single-value {
    color: #212529;
  }
  
  .select__placeholder {
    color: #6c757d;
  }
  
  .select__indicator-separator {
    display: none;
  }
  
  .select__dropdown-indicator {
    color: #6c757d;
    
    &:hover {
      color: #495057;
    }
  }
  
  .select__menu {
    z-index: 1050;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  .select__option {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &.select__option--is-selected {
      background-color: #0d6efd;
    }
    
    &.select__option--is-focused {
      background-color: #e9ecef;
    }
  }
  
  // Responsive adjustments
  @media (max-width: 576px) {
    .row {
      --bs-gutter-x: 0.5rem;
    }
    
    .select__control {
      min-height: 36px;
      font-size: 0.875rem;
    }
    
    .select__value-container {
      padding: 2px 6px;
    }
  }
}
