/**
 * Image preloader utility for better performance
 */

interface PreloadOptions {
  priority?: 'high' | 'low';
  as?: 'image';
  crossorigin?: 'anonymous' | 'use-credentials';
}

/**
 * Preload critical images to improve perceived performance
 */
export const preloadImage = (src: string, options: PreloadOptions = {}): Promise<void> => {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = options.as || 'image';
    link.href = src;
    
    if (options.crossorigin) {
      link.crossOrigin = options.crossorigin;
    }
    
    // Add fetchpriority if supported
    if ('fetchPriority' in link && options.priority) {
      (link as any).fetchPriority = options.priority;
    }
    
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to preload image: ${src}`));
    
    document.head.appendChild(link);
  });
};

/**
 * Preload multiple images
 */
export const preloadImages = async (urls: string[], options: PreloadOptions = {}): Promise<void[]> => {
  const promises = urls.map(url => preloadImage(url, options));
  return Promise.all(promises);
};

/**
 * Create an image preloader for a specific set of images
 */
export class ImagePreloader {
  private cache = new Set<string>();
  private loading = new Set<string>();

  async preload(src: string, options: PreloadOptions = {}): Promise<void> {
    if (this.cache.has(src) || this.loading.has(src)) {
      return;
    }

    this.loading.add(src);
    
    try {
      await preloadImage(src, options);
      this.cache.add(src);
    } catch (error) {
      console.warn('Failed to preload image:', src, error);
    } finally {
      this.loading.delete(src);
    }
  }

  isPreloaded(src: string): boolean {
    return this.cache.has(src);
  }

  isLoading(src: string): boolean {
    return this.loading.has(src);
  }

  clear(): void {
    this.cache.clear();
    this.loading.clear();
  }
}

// Global image preloader instance
export const globalImagePreloader = new ImagePreloader();

/**
 * Preload images that are likely to be needed soon
 */
export const preloadCriticalImages = (imageUrls: string[]) => {
  // Only preload first few images to avoid overwhelming the network
  const criticalImages = imageUrls.slice(0, 6);
  
  criticalImages.forEach((url, index) => {
    // Stagger preloading to avoid blocking other resources
    setTimeout(() => {
      globalImagePreloader.preload(url, { priority: index < 3 ? 'high' : 'low' });
    }, index * 100);
  });
};
