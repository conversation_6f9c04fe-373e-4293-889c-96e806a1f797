@use '@/variables' as *;

.flirt-card {
  border: 1px solid $light-gray-color;
  border-radius: 12px;
  background: $white-color;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .avatar-container {
    width: 50px;
    height: 50px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid $light-gray-color;
    
    &.loading {
      opacity: 0;
    }
    
    &.loaded {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  .avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .username {
    color: $black-color;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 4px;
  }

  .flirt-type {
    font-size: 12px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    
    &.sent {
      background-color: #e3f2fd;
      color: #1976d2;
    }
    
    &.received {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }
  }

  .time {
    color: $link-color;
    font-size: 12px;
    font-weight: 400;
  }

  .flirt-message {
    p {
      color: $black-color;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.5;
    }
  }

  .action-btn {
    &:hover {
      background-color: rgba(0, 0, 0, 0.05) !important;
      border-radius: 50%;
    }
    
    &:focus {
      box-shadow: none !important;
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .flirt-card {
    .card-body {
      padding: 12px !important;
    }
    
    .avatar-container,
    .user-avatar,
    .avatar-placeholder {
      width: 40px;
      height: 40px;
    }
    
    .username {
      font-size: 14px;
    }
    
    .flirt-message p {
      font-size: 13px;
    }
  }
}
