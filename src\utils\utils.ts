export const getAppOriginURL = () => {
    return `${window.location.origin}${import.meta.env.VITE_BASE_URL?.length > 1 ? import.meta.env.VITE_BASE_URL : ""}`;
};

export const calculateAgeByDOB = (dob: string | undefined) => {
    if (!dob) return null;

    const birthDate = new Date(dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();

    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--;
    }

    return `, ${age}`;
};


export const stringToArray = (value: string): string[] => {
    return value ? value.split(",").filter(Boolean) : [];
};

export const arrayToString = (array: string[]): string => {
    return array.join(",");
};

export const toggleItemInArray = (array: string[], item: string): string[] => {
    return array.includes(item)
        ? array.filter((i) => i !== item)
        : [...array, item];
};

export const removeItemFromArray = (array: string[], item: string): string[] => {
    return array.filter((i) => i !== item);
};

export const FormatS3ImgUrl = (filename: string) => {
    return `${import.meta.env.VITE_S3_BASE_URL}${filename}`
}

// Image optimization utility types
export type ImageSize = 'thumbnail' | 'medium' | 'large' | 'original';
export type ImageFormat = 'webp' | 'jpg' | 'png';

export interface ImageOptimizationOptions {
  size?: ImageSize;
  format?: ImageFormat;
  quality?: number;
  blur?: boolean;
}

// Image size configurations
const IMAGE_SIZE_CONFIG: Record<ImageSize, { width?: number; height?: number; quality: number }> = {
  thumbnail: { width: 300, height: 300, quality: 75 },
  medium: { width: 600, height: 600, quality: 85 },
  large: { width: 1200, height: 1200, quality: 90 },
  original: { quality: 95 }
};

/**
 * Generate optimized image URL with size and format parameters
 * This assumes your S3/CDN supports query parameters for image transformation
 * If not, you'll need to implement server-side image processing
 */
export const getOptimizedImageUrl = (
  filename: string,
  options: ImageOptimizationOptions = {}
): string => {
  if (!filename) return '';

  const {
    size = 'medium',
    format = 'webp',
    quality,
    blur = false
  } = options;

  const baseUrl = `${import.meta.env.VITE_S3_BASE_URL}${filename}`;
  const config = IMAGE_SIZE_CONFIG[size];

  // Build query parameters for image optimization
  const params = new URLSearchParams();

  if (config.width && config.height && size !== 'original') {
    params.append('w', config.width.toString());
    params.append('h', config.height.toString());
    params.append('fit', 'cover');
  }

  if (quality || config.quality) {
    params.append('q', (quality || config.quality).toString());
  }

  if (format !== 'jpg') {
    params.append('fm', format);
  }

  if (blur) {
    params.append('blur', '20');
  }

  // Auto format selection based on browser support
  params.append('auto', 'format,compress');

  return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
};

/**
 * Generate multiple image URLs for responsive loading
 */
export const getResponsiveImageUrls = (filename: string) => {
  if (!filename) return {};

  return {
    thumbnail: getOptimizedImageUrl(filename, { size: 'thumbnail', format: 'webp' }),
    medium: getOptimizedImageUrl(filename, { size: 'medium', format: 'webp' }),
    large: getOptimizedImageUrl(filename, { size: 'large', format: 'webp' }),
    original: getOptimizedImageUrl(filename, { size: 'original' }),
    // Blur placeholder for progressive loading
    placeholder: getOptimizedImageUrl(filename, {
      size: 'thumbnail',
      format: 'webp',
      quality: 10,
      blur: true
    })
  };
};

/**
 * Check if browser supports WebP format
 */
export const supportsWebP = (): boolean => {
  if (typeof window === 'undefined') return false;

  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;

  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
};

/**
 * Get optimal image format based on browser support
 */
export const getOptimalImageFormat = (): ImageFormat => {
  return supportsWebP() ? 'webp' : 'jpg';
};

export const FormatLocation = (country?: string, city?: string): string => {
    if (country && city) return `${country}, ${city}`;
    if (country) return country;
    if (city) return city;
    return "";
};