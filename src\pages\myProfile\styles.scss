@use "@/variables" as *;

.my-profile {
  min-height: calc(100dvh - 73px);
  padding: 24px 0;
  background: radial-gradient(50% 50% at 50% 50%, #feebd0 0%, #ffdeb7 100%);

  &-menus {
    border-radius: 24px;
    background-color: $white-color;

    .profile-box {
      padding: 24px;
      border-radius: 24px 24px 0 0;
      background: $white-color;

      .profile-upload {
        padding: 20px 0;

        .circle {
          width: 150px;
          height: 150px;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 5;
          }

          &.has-image:hover {
            &::after {
              opacity: 1;
            }

            .delete-icon {
              opacity: 1;
            }
          }

          .p-image {
            position: absolute;
            bottom: 10px;
            right: 0;
            background: $black-color;
            border: 3px solid $white-color;
            cursor: pointer;
            width: 40px;
            height: 40px;

            .file-upload {
              opacity: 0;
              position: absolute;
              top: 0;
              left: 0;
              width: 34px;
              height: 34px;
              z-index: 9;
            }
          }
        }
      }
    }

    .nav-pills {
      min-height: calc(100dvh - 490px);

      .nav-link {
        padding: 18px 24px;
        border-radius: 0;
        font-size: 18px;
        color: $black-color;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &::after {
          content: "";
          display: inline-block;
          width: 32px;
          height: 32px;
          background-image: url("../../assets/images/right-arrow.svg");
          background-repeat: no-repeat;
          background-size: 32px;
        }

        &.active {
          background-color: $link-color;
          color: $white-color;

          &::after {
            filter: invert(1);
          }

          &:hover {
            background-color: $link-color;
          }
        }

        &:hover {
          background-color: $light-gray-color;
        }
      }

      .logout-link {
        margin-top: auto;
        border-top: 1px solid $light-gray-color;

        .nav-link {
          margin-top: auto;
          border-radius: 0 0 24px 24px;
        }
      }
    }
  }

  &-content {
    border-radius: 24px;
    background: $white-color;
    padding: 30px;

    .content-heading {
      margin-bottom: 30px;
      max-width: 650px;
    }

    .change-password {
      form {
        max-width: 500px;
      }
    }

    .my-photos {
      gap: 20px;

      &-img {
        width: 230px;
        height: 230px;
        object-fit: cover;
        border-radius: 12px;
      }

      &-upload {
        width: 230px;
        height: 230px;
        border-radius: 12px;
        border: 1px dashed $black-color;
        background: $white-color;

        .photo-upload {
          opacity: 0;
          position: absolute;
          top: 0;
          left: 0;
          width: 230px;
          height: 230px;
          z-index: 9;
        }
      }
    }

    .notification-box {
      padding: 18px 0;
      border-bottom: 1px solid $light-gray-color;

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
        border-bottom: 0;
      }
    }

    .accordion {
      &-item {
        border: 0;
        background: transparent;

        &:first-child {
          .accordion-button {
            padding-top: 0;
          }
        }
      }

      &-button {
        font-size: 20px;
        font-weight: 700;
        color: $black-color;
        padding: 24px 0;
        background: $white-color;
        box-shadow: none;
        border-radius: 0 !important;
        border-bottom: 1px solid $light-gray-color;

        &::after {
          background-image: url(../../assets/images/right-arrow.svg);
          background-size: 30px;
          transform: rotate(90deg);
          width: 30px;
          height: 30px;
          transition: 0.3s ease-in-out;
        }

        &:not(.collapsed) {
          &::after {
            transform: rotate(-90deg);
          }
        }
      }

      &-collapse {
        background: $white-color;
        border-radius: 0;
        border-bottom: 1px solid $light-gray-color;
      }

      &-body {
        padding: 24px 0;

        .form-input-group {
          gap: 30px;
        }

        .form-input {
          width: calc(50% - 15px);
        }

        .form-check {
          padding: 18px 40px 18px 60px;
          border-radius: 8px;
          border: 1px solid $light-gray-color;
          margin-bottom: 0;
        }

        .select__multi-value {
          background-color: $primary-color;
          border-radius: 5px;

          &__label {
            color: $white-color;
            padding: 4px 8px;
            font-size: 14px;
            font-weight: 500;
          }

          &__remove {
            color: $white-color;
            cursor: pointer;
            padding: 4px 6px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              background-color: rgba(255, 255, 255, 0.2);
              color: $white-color;
            }

            &::before {
              content: "×";
              font-size: 25px;
              font-weight: bold;
              line-height: 1;
              display: block;
            }

            svg {
              display: none;
            }
          }
        }

        .select__indicators {
          .select__clear-indicator,
          .select__dropdown-indicator {
            cursor: pointer;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
              color: $link-color;
            }

            svg {
              width: 20px;
              height: 20px;
              display: block;
            }
          }

          .select__clear-indicator {
            &::before {
              content: "×";
              font-size: 25px;
              font-weight: bold;
              display: block;
              line-height: 1;
            }

            svg {
              display: none;
            }
          }

          .select__indicator-separator {
            background-color: $light-gray-color;
            margin: 8px 0;
            width: 1px;
          }
        }

        .select__menu {
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          border: 1px solid $light-gray-color;
          margin-top: 4px;
        }

        .select__option {
          padding: 12px 16px;
          cursor: pointer;

          &--is-focused {
            background-color: rgba($link-color, 0.1);
            color: $black-color;
          }

          &--is-selected {
            background-color: $link-color;
            color: $white-color;
          }
        }

        .select__placeholder {
          color: #999;
          font-size: 16px;
        }
      }

      .dob-dropdown {
        .select__control {
          padding: 12px 16px;
        }
      }
    }
  }
}

@media (max-width: "1399px") {
  .my-profile {
    &-menus {
      .profile-box {
        h5 {
          font-size: 18px;
        }

        p {
          font-size: 14px;
        }

        .profile-upload {
          .circle {
            width: 130px;
            height: 130px;
          }
        }
      }

      .nav-pills {
        .nav-link {
          padding: 14px 24px;
          font-size: 16px;

          &::after {
            width: 28px;
            height: 28px;
            background-size: 28px;
          }
        }
      }
    }

    &-content {
      .accordion {
        &-button {
          padding: 20px 0;
          font-size: 18px;

          &::after {
            width: 28px;
            height: 28px;
            background-size: 28px;
          }
        }

        &-body {
          padding: 20px 0;

          .form-input-group {
            gap: 20px;
          }
        }
      }

      .my-photos {
        &-img {
          width: 180px;
          height: 180px;
        }

        &-upload {
          width: 180px;
          height: 180px;

          .photo-upload {
            width: 180px;
            height: 180px;
          }
        }
      }
    }
  }
}

@media (max-width: "1199px") {
  .my-profile {
    h3 {
      font-size: 24px;
    }

    &-content {
      .accordion {
        &-body {
          .form-input {
            width: 100%;
          }
        }
      }

      .my-photos {
        gap: 15px;

        &-img {
          width: 150px;
          height: 150px;
        }

        &-upload {
          width: 150px;
          height: 150px;

          h6 {
            font-size: 14px;
          }

          svg {
            width: 45px;
            height: 45px;
          }

          .photo-upload {
            width: 150px;
            height: 150px;
          }
        }
      }
    }
  }
}

@media (max-width: "991px") {
  .my-profile {
    min-height: calc(100dvh - 56px);

    &-content {
      .btn {
        min-width: 150px;
        font-size: 16px;
      }
    }
  }
}

@media (max-width: "767px") {
  .my-profile {
    h3 {
      font-size: 20px;
    }

    .row {
      row-gap: 16px;
    }

    &-menus {
      .nav-pills {
        min-height: auto;
      }
    }

    &-content {
      padding: 20px;

      .btn {
        min-width: 130px;
      }

      .accordion {
        &-button {
          padding: 16px 0;
          font-size: 16px;

          &::after {
            width: 24px;
            height: 24px;
            background-size: 24px;
          }
        }

        &-body {
          padding: 16px 0;

          .form-input-group {
            gap: 16px;

            .form-check {
              padding: 13px 30px 13px 50px;
            }

            // Mobile responsive styles for React Select
            .select__control {
              min-height: 44px;
            }

            .select__multi-value {
              margin: 1px;

              &__label {
                padding: 3px 6px;
                font-size: 13px;
              }

              &__remove {
                padding: 3px 5px;

                &::before {
                  font-size: 14px;
                }
              }
            }

            .select__indicators {
              .select__clear-indicator,
              .select__dropdown-indicator {
                padding: 6px;

                &::before {
                  font-size: 16px;
                }
              }

              .select__dropdown-indicator::before {
                font-size: 10px;
              }
            }
          }
        }
      }

      .my-photos {
        &-img {
          min-width: 140px;
          max-width: calc(50% - 7.5px);
          flex: 1;
        }

        &-upload {
          min-width: 140px;
          max-width: calc(50% - 7.5px);
          flex: 1;

          .photo-upload {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
