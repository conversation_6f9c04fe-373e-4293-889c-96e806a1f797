@use '@/variables' as *;

.advance-filter-offcanvas {
    .offcanvas-header {
        padding: 24px;
        border-bottom: 1px solid $light-gray-color;

        .offcanvas-title {
            color: $black-color;
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .btn-close {
            opacity: 1;
        }
    }

    .offcanvas-body {
        padding: 0;
        
        &.filter-sections-container {
            overflow-y: hidden;
            display: flex;
            flex-direction: column;
            
            .filter-sections {
                padding: 24px;
                overflow-y: auto;
                height: 100%;
                display: flex;
                flex-direction: column;
                gap: 24px;
                
                .filter-section {
                    padding-bottom: 16px;
                    border-bottom: 1px solid $light-gray-color;
                    
                    &:last-child {
                        border-bottom: none;
                    }
                    
                    .filter-section-title {
                        color: $black-color;
                        font-size: 15px;
                        font-weight: 600;
                        line-height: normal;
                        margin-bottom: 16px;
                    }
                    
                    .filter-options-list {
                        max-height: none;
                    }
                    
                    .hair-color-selector {
                        .hair-radio {
                            .hair-color-box {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                cursor: pointer;
                                gap: 4px;

                                .color-outer-ring {
                                    padding: 2px;
                                    border-radius: 50%;
                                    border: 2px solid;
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 40px;
                                    height: 40px;
                                }

                                .color-inner-circle {
                                    width: 34px;
                                    height: 34px;
                                    min-width: 34px;
                                    border-radius: 50%;
                                }

                                .color-label {
                                    color: $black-color;
                                    font-size: 10px;
                                    font-weight: 400;
                                    line-height: normal;
                                }
                            }

                            input[type='radio']:checked + .hair-color-box .color-outer-ring {
                                box-shadow: 0 0 0 1px $black-color;
                            }
                        }
                    }
                }
            }
        }
    }

    .filter-btns {
        border-top: 1px solid $light-gray-color;
        background: $white-color;
        box-shadow: 0px -4px 24px 0px rgba(0, 0, 0, 0.15);
        padding: 24px;

        .first-btn {

            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 15px 40px;
            color: $black-color;
            font-size: 14px;
            min-width: auto;

            &:hover {
                background: #79259C;
                color: $white-color;
            }
        }

        .second-btn {
            padding: 15px 40px;
            font-size: 14px;
            min-width: auto;
        }
    }
}