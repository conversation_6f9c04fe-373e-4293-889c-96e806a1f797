@use "@/variables" as *;

.profile-management {
  min-height: calc(100dvh - 73px);
  padding: 80px 0;
  background: radial-gradient(50% 50% at 50% 50%, #feebd0 0%, #ffdeb7 100%);

  &-inner {
    gap: 20px;
  }

  .profile-box {
    padding: 50px;
    border-radius: 10px;
    background: $white-color;
    margin-bottom: 20px;

    .profile-upload {
      padding: 20px 0;

      .circle {
        width: 150px;
        height: 150px;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 50%;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 5;
        }

        &.has-image:hover {
          &::after {
            opacity: 1;
          }

          .delete-icon {
            opacity: 1;
          }
        }

        .p-image {
          position: absolute;
          bottom: 10px;
          right: 0;
          background: $black-color;
          border: 3px solid $white-color;
          cursor: pointer;
          width: 40px;
          height: 40px;

          .file-upload {
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
            width: 34px;
            height: 34px;
            z-index: 9;
          }
        }
      }
    }
  }

  .accordion {
    gap: 20px;

    &-item {
      border: 0;
      background: transparent;
    }

    &-button {
      font-size: 20px;
      font-weight: 700;
      color: $black-color;
      padding: 24px 50px;
      background: $white-color;
      box-shadow: none;
      border-radius: 10px 10px 0 0 !important;

      &::after {
        background-image: url(../../assets/images/right-arrow.svg);
        background-size: 30px;
        transform: rotate(90deg);
        width: 30px;
        height: 30px;
        transition: 0.3s ease-in-out;
      }

      &:not(.collapsed) {
        &::after {
          transform: rotate(-90deg);
        }
      }

      &.collapsed {
        border-radius: 10px !important;
      }
    }

    &-collapse {
      margin-top: 1px;
      background: $white-color;
      border-radius: 0 0 10px 10px;
    }

    &-body {
      padding: 50px;

      .form-input-group {
        gap: 30px;
      }

      .form-input {
        width: calc(50% - 15px);
      }

      .form-check {
        padding: 18px 40px 18px 60px;
        border-radius: 8px;
        border: 1px solid $light-gray-color;
        margin-bottom: 0;
      }

      .select__control {
        min-height: 48px;
        box-shadow: none;

        &:hover {
          border-color: $link-color;
        }
      }

      .select__multi-value {
        background-color: $primary-color;
        border-radius: 5px;

        &__label {
          color: $white-color;
          padding: 4px 8px;
          font-size: 14px;
          font-weight: 500;
        }

        &__remove {
          color: $white-color;
          cursor: pointer;
          padding: 4px 6px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: $white-color;
          }

          &::before {
            content: "×";
            font-size: 25px;
            font-weight: bold;
            line-height: 1;
            display: block;
          }

          svg {
            display: none;
          }
        }
      }

      .select__indicators {
        .select__clear-indicator,
        .select__dropdown-indicator {
          cursor: pointer;
          padding: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: $link-color;
          }

          svg {
            width: 20px;
            height: 20px;
            display: block;
          }
        }

        .select__clear-indicator {
          &::before {
            content: "×";
            font-size: 25px;
            font-weight: bold;
            display: block;
            line-height: 1;
          }

          svg {
            display: none;
          }
        }

        .select__indicator-separator {
          background-color: $light-gray-color;
          margin: 8px 0;
          width: 1px;
        }
      }

      .select__menu {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid $light-gray-color;
        margin-top: 4px;
      }

      .select__option {
        padding: 12px 16px;
        cursor: pointer;

        &--is-focused {
          background-color: rgba($link-color, 0.1);
          color: $black-color;
        }

        &--is-selected {
          background-color: $link-color;
          color: $white-color;
        }
      }

      .select__placeholder {
        color: #999;
        font-size: 16px;
      }
    }

    .dob-dropdown {
      .select__control {
        padding: 12px 16px;
      }
    }
  }
}

@media (max-width: "1599px") {
  .profile-management {
    padding: 50px 0;
  }
}

@media (max-width: "1399px") {
  .profile-management {
    padding: 30px 0;

    .profile-box {
      padding: 40px;

      h5 {
        font-size: 18px;
      }
    }

    .accordion {
      &-button {
        padding: 20px 40px;
        font-size: 18px;
      }

      &-body {
        padding: 40px;

        .form-input-group {
          gap: 20px;
        }
      }
    }
  }
}

@media (max-width: "767px") {
  .profile-management {
    min-height: calc(100dvh - 56px);
    padding: 30px 0;

    .profile-box {
      padding: 30px;

      h5 {
        font-size: 18px;
      }

      .profile-upload {
        .circle {
          width: 130px;
          height: 130px;
        }
      }
    }

    .accordion {
      gap: 16px;

      &-button {
        padding: 16px 30px;
        font-size: 16px;

        &::after {
          width: 24px;
          height: 24px;
          background-size: 24px;
        }
      }

      &-body {
        padding: 30px;

        .form-input {
          width: 100%;
        }

        .form-check {
          padding: 13px 30px 13px 50px;
        }

        // Mobile responsive styles for React Select
        .select__control {
          min-height: 44px;
        }

        .select__multi-value {
          margin: 1px;

          &__label {
            padding: 3px 6px;
            font-size: 13px;
          }

          &__remove {
            padding: 3px 5px;

            &::before {
              font-size: 14px;
            }
          }
        }

        .select__indicators {
          .select__clear-indicator,
          .select__dropdown-indicator {
            padding: 6px;

            &::before {
              font-size: 16px;
            }
          }

          .select__dropdown-indicator::before {
            font-size: 10px;
          }
        }
      }
    }
  }
}
