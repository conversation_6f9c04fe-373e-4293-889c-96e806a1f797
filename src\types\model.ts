export interface ModelInterface {
  id: number;
  username: string;
  email: any;
  model_profile: ModelProfile;
  status: string;
  createdAt: string;
  avatar: string;
  city: string;
  country: string;
  modelId: number;
  isFavorite: boolean;
  refetchModels?: () => void;
  dob: string | undefined;
}

export interface ModelProfile {
  dob: string | undefined;
  aboutMe: string | undefined;
  gender: Record<string, any>;
  personality: Record<string, any>;
  height: string | undefined;
}
