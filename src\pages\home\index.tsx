import { useActivePackage, useInfiniteModels } from "@/api";
import ChatPopup from "@/components/chatbot/ChatPopup";
import { Header } from "@/components/common";
import SearchBar from "@/components/common/Search";
import InfiniteMembersList from "@/components/memberslist/InfiniteMembersList";
import { useAutoScroll, useFormattedFilters } from "@/hooks";
import { useTranslation } from "@/hooks/useTranslation";
import { setUserDetails } from "@/stores";
import useUserStore from "@/stores/user";
import { useEffect, useMemo, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import "./styles.scss";

interface HomeProps {
  isFavorite?: boolean;
}

const Home = ({ isFavorite }: HomeProps) => {
  const { t } = useTranslation();
  const initialFilter = {
    name: undefined,
    relationshipStatusId: undefined,
    hairColorId: undefined,
    bestFeatureId: undefined,
    eyeColorId: undefined,
    personalityId: undefined,
    appearanceId: undefined,
    bodyTypeId: undefined,
    smokingHabitId: undefined,
    drinkingHabitId: undefined,
    interestIds: undefined,
    ageRange: undefined,
    distance: undefined,
    withPhotos: undefined,
    isFavorite: undefined,
  };

  const limit = 50;
  const [filters, setFilters] = useState(initialFilter);
  const [showMobileSearch, setShowMobileSearch] = useState<boolean>(false);
  const formattedFilters = useFormattedFilters(filters);
  const [params, setParams] = useState(formattedFilters);
  const { data: { data: packageData = {} } = {} } = useActivePackage();
  const userData = useUserStore((state) => state.userInfo.user);
  const barRef = useRef<HTMLDivElement | null>(null);
  const [isSticky, setIsSticky] = useState(false);

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    isError,
    refetch: refetchModels,
  } = useInfiniteModels({
    params: { ...params, limit, isFavorite },
  });

  const models = useMemo(() => {
    return data?.pages?.flatMap((page) => page?.data?.models || []) || [];
  }, [data]);

  useAutoScroll();

  const onClearFilters = () => {
    setFilters(initialFilter);
    setParams(initialFilter);
  };

  const onSearchWithFilters = () => {
    setParams(formattedFilters);
  };

  useEffect(() => {
    if (packageData?.package) {
      setUserDetails({
        ...userData,
        coins: packageData?.coins,
      });
    }
  }, [packageData]);

  useEffect(() => {
    const handleScroll = () => {
      if (!barRef.current) return;
      const rect = barRef.current.getBoundingClientRect();

      console.log(rect.top);
      if (window.scrollY > rect.top + 66) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div>
      <Header
        showMobileSearch={showMobileSearch}
        setShowMobileSearch={setShowMobileSearch}
      >
        <div
          className={`search-bar-container floating-search-dropdown container-fluid ${showMobileSearch ? "d-block" : "d-none"}`}
        >
          <SearchBar
            filters={filters}
            setFilters={setFilters}
            onClearFilters={onClearFilters}
            onSearchWithFilters={onSearchWithFilters}
            setParams={setParams}
          />
        </div>
      </Header>
      <div className="banner-img">
        <Container fluid>
          <div className="d-flex flex-column gap-2 mb-3">
            <h2 className="title mb-0">{t("home.title")}</h2>
            <p className="description mb-0">{t("home.description")}</p>
          </div>
        </Container>
       { 
        window.innerWidth > 1150 && (
        <div
        ref={barRef}
        className={`bg-white w-100 navbar-search-bar ${
          isSticky ? "position-fixed" : "position-static"
        }`}
        style={isSticky ? { top: "66px", zIndex: 1000 } : {}}
      >
        <SearchBar
          filters={filters}
          setFilters={setFilters}
          onClearFilters={onClearFilters}
          onSearchWithFilters={onSearchWithFilters}
          setParams={setParams}
        />
      </div>
        )
      }
      </div>
      <div className="members-list">
        <Container fluid>
          {isError || (models.length === 0 && !isLoading) ? (
            <div className="text-center py-5">
              <h5 className="fw-bold mb-2">No matches found</h5>
              <p className="text-muted mb-0">
                We couldn't find any models matching your preferences right now.
                <br />
                Try adjusting your filters or check back later for new faces!
              </p>
            </div>
          ) : (
            <InfiniteMembersList
              models={models}
              hasNextPage={hasNextPage}
              isLoading={isLoading}
              isFetchingNextPage={isFetchingNextPage}
              fetchNextPage={fetchNextPage}
              refetchModels={refetchModels}
            />
          )}
        </Container>
      </div>
      <ChatPopup />
    </div>
  );
};

export default Home;
