import React, { useState, useRef, useEffect, useCallback } from "react";
import { Image } from "react-bootstrap";
import defaultProfile from "@/assets/images/user.png";
import { getOptimizedImageUrl, getResponsiveImageUrls, ImageSize, getOptimalImageFormat } from "@/utils/utils";
import './styles.scss'

interface LazyImageProps {
  src?: string;
  alt?: string;
  className?: string;
  size?: ImageSize;
  enableProgressiveLoading?: boolean;
  rootMargin?: string;
  threshold?: number;
  onLoad?: () => void;
  onError?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = "",
  className = "",
  size = 'medium',
  enableProgressiveLoading = true,
  rootMargin = '50px',
  threshold = 0.1,
  onLoad,
  onError
}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [showPlaceholder, setShowPlaceholder] = useState(enableProgressiveLoading);
  const imgRef = useRef<HTMLImageElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Generate optimized image URLs
  const imageUrls = src ? getResponsiveImageUrls(src) : null;
  const optimizedSrc = src ? getOptimizedImageUrl(src, {
    size,
    format: getOptimalImageFormat()
  }) : defaultProfile;
  const placeholderSrc = imageUrls?.placeholder;

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.disconnect();
        }
      },
      {
        rootMargin,
        threshold
      }
    );

    if (wrapperRef.current) {
      observer.observe(wrapperRef.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold]);

  // Handle image load
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setShowPlaceholder(false);
    onLoad?.();
  }, [onLoad]);

  // Handle image error
  const handleError = useCallback(() => {
    setIsError(true);
    setShowPlaceholder(false);
    onError?.();
  }, [onError]);

  // Progressive loading effect
  useEffect(() => {
    if (enableProgressiveLoading && placeholderSrc && isIntersecting && !isLoaded) {
      // Load placeholder first, then main image
      const placeholderImg = new window.Image();
      placeholderImg.onload = () => {
        // Small delay to show blur effect
        setTimeout(() => {
          setShowPlaceholder(false);
        }, 100);
      };
      placeholderImg.src = placeholderSrc;
    }
  }, [enableProgressiveLoading, placeholderSrc, isIntersecting, isLoaded]);

  return (
    <div
      ref={wrapperRef}
      className={`lazy-image-wrapper position-relative ${className}`}
    >
      {/* Loading skeleton */}
      {!isIntersecting && (
        <div className="image-skeleton position-absolute top-0 start-0 w-100 h-100" />
      )}

      {/* Blur placeholder for progressive loading */}
      {enableProgressiveLoading && showPlaceholder && placeholderSrc && isIntersecting && (
        <Image
          src={placeholderSrc}
          alt={alt}
          className="placeholder-blur position-absolute top-0 start-0 w-100 h-100"
          style={{ filter: 'blur(10px)', transform: 'scale(1.1)' }}
        />
      )}

      {/* Main image */}
      {isIntersecting && (
        <Image
          ref={imgRef}
          src={optimizedSrc}
          alt={alt}
          className={`member-img w-100 transition-opacity ${
            isLoaded ? "opacity-100" : "opacity-0"
          } ${isError ? "error" : ""}`}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
          decoding="async"
        />
      )}

      {/* Error state */}
      {isError && (
        <div className="error-state position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
          <div className="text-muted small">Failed to load image</div>
        </div>
      )}
    </div>
  );
};

export default LazyImage;
