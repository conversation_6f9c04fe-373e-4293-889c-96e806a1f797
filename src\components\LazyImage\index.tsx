import React, { useState } from "react";
import { Image } from "react-bootstrap";
import defaultProfile from "@/assets/images/user.png";
import './styles.scss'

interface LazyImageProps {
  src?: string;
  alt?: string;
  className?: string;
}

const LazyImage: React.FC<LazyImageProps> = ({ src, alt = "", className }) => {
  const [loaded, setLoaded] = useState(false);

  return (
    <div className={`lazy-image-wrapper position-relative ${className}`}>
      {!loaded && (
        <div className="image-skeleton position-absolute top-0 start-0 w-100 h-100" />
      )}
      <Image
        src={src || defaultProfile}
        alt={alt}
        className={`member-img w-100 ${loaded ? "visible" : "invisible"}`}
        onLoad={() => setLoaded(true)}
        loading="eager"
      />
    </div>
  );
};

export default LazyImage;
