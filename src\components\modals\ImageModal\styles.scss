@use "@/variables" as *;

.image-modal {
  .modal-dialog {
    max-width: 90vw;
    margin: 1rem auto;
  }

  .modal-content {
    background: #fff;
    border: none;
    border-radius: 0.5rem;
    overflow: hidden;
    max-height: 90dvh;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    background: transparent;
    padding: 1rem 1.5rem 0;

    .modal-title {
      font-size: 1.1rem;
      font-weight: 500;

      .image-counter {
        font-size: 0.9rem;
        opacity: 0.8;
      }
    }

    .btn-close-custom {
      border: none;
      background: none;
      padding: 0;

      &:hover {
        opacity: 0.8;
      }

      &:focus {
        box-shadow: none;
      }
    }
  }

  .modal-body {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    @media (max-width: 768px) {
      max-height: calc(100% - 140px);
    }
  }

  .carousel-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-50%) scale(1.1);
    }

    &.carousel-btn-prev {
      left: -2rem;
    }

    &.carousel-btn-next {
      right: -2rem;
    }
  }

  .image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: unset;
    max-height: 100%;
    overflow: hidden;

    .modal-image {
      max-height: calc(90dvh - 190px);
      max-width: 100%;
      object-fit: contain;
      border-radius: 0.25rem;

      @media (max-width: 768px) {
        max-height: 100%;
      }
    }
  }

  .modal-footer {
    background: transparent;
    padding: 1rem 1.5rem 1.5rem;
    max-height: 120px;
    overflow-x: auto;
    overflow-y: hidden;

    .thumbnail {
      width: 80px;
      height: 80px;
      border-radius: 0.25rem;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      flex-shrink: 0;

      &:hover {
        border-color: rgba(255, 255, 255, 0.5);
      }

      &.active {
        border-color: $primary-color;
      }

      .thumbnail-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .modal-dialog {
      max-width: 95vw;
      margin: 0.5rem auto;
    }

    .modal-header {
      padding: 0.75rem 1rem 0;

      .modal-title {
        font-size: 1rem;

        .image-counter {
          font-size: 0.8rem;
        }
      }
    }

    .image-container {
      min-height: unset;
      max-height: 100%;

      .carousel-btn {
        width: 40px;
        height: 40px;

        &.carousel-btn-prev {
          left: 0.5rem;
        }

        &.carousel-btn-next {
          right: 0.5rem;
        }
      }

      .carousel-indicators {
        bottom: 0.5rem;

        .indicator-dot {
          width: 10px;
          height: 10px;
        }
      }
    }

    .modal-footer {
      padding: 0.75rem 1rem 1rem;
      max-height: 80px;

      .thumbnail {
        width: 50px;
        height: 50px;
      }
    }
  }

  @media (max-width: 768px) {
    .modal-dialog {
      max-width: 100vw;
      margin: 0;
      height: 100vh;
    }

    .modal-content {
      height: 75dvh;
      border-radius: 0;
      width: 90%;
      margin: auto;
      padding: 10px;
      max-height: 75dvh;
    }

    .image-container {
      min-height: unset;
      max-height: 100%;

      .carousel-btn {
        width: 36px;
        height: 36px;
      }
    }

    .modal-footer {
      .thumbnail {
        width: 60px;
        height: 60px;
      }
    }
  }
}

.shrink-0 {
  flex-shrink: 0 !important;
}

.w-max-content {
  width: max-content !important;
}

.image-modal-scroller {
  &::-webkit-scrollbar {
    height: 8px;
    @media (max-width: 991px) {
      height: 6px;
    }
  }
}

.button-icon {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  border: 1px solid #c8c8c8;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}