@use '@/variables' as *;

.search-bar-wrapper {
    border-radius: 0px;
    background: $white-color;
    padding-block: 12px;
    border: 1px solid $light-gray-color;
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.05);
    position: relative;

    .custom-interest-dropdown {
        position: relative;
    }
    

    .mobile-header {
        display: flex;
        justify-content: space-between;
        padding-inline: 10px;
        padding-top: 10px;
        .close-button {
            background: transparent;
            border: none;
            box-shadow: none;
        }
        h5 {
            margin-bottom: 0px;
        }
    }

    .form-input-group {
        gap: 14px;
        width: 100%;

        .form-input {
            .form-control {
                color: $black-color;
                font-size: 14px;
                font-weight: 500;
                line-height: normal;

                &::placeholder {
                    color: $black-color;
                    opacity: 1;
                }
            }

            .select__placeholder {
                color: $black-color;
            }

            .select__dropdown-indicator {
                svg {
                    color: $black-color;
                }
            }

            .select {
                &__control {
                    padding: 12px 16px;
                    width: 170px;
                    color: $black-color;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: normal;
                    height: 52px;
                }
            }

            .custom-dropdown {
                padding: 12px 16px;
                width: 170px;
                color: $black-color;
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
                height: 52px;
                background: transparent;
                border: 1px solid #E7E7E7;
                border: 1px solid #E7E7E7;
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-width: auto;
                position: relative;
              
            }

            .dropdown-toggle::after {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z'%3E%3C/path%3E%3C/svg%3E");
                background-size: 24px;
                width: 24px;
                height: 24px;
                margin-left: 0px;
                border: none;
            }

            .dropdown-menu {
                border-radius: 8px;
                background: $white-color;
                box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.10);
                padding: 16px;
                border: none;
                width: 170px;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .dropdown-item {
                    padding: 0px;
                    background-color: transparent;

                    .form-check-label {
                        color: $black-color;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: normal;
                        margin-top: 3px;
                    }

                    &:active {
                        background-color: transparent;
                    }
                }
            }
        }

        .search-input {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            align-items: center;
            gap: 10px;
            display: flex;
            width: 100%;
            max-width: unset;
            height: 52px;
            flex: 3;

            &.form-input {
                svg {
                    position: unset;
                    width: 17px;
                    height: 17px;

                }
            }

        }
        
        .dropdown {
            flex: 2;
        }

        .img-filter {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            color: $black-color;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            height: 52px;
            min-width: 170px;

            .custom-checkbox {
                .form-check-label {
                    margin-top: 3px;
                    color: $black-color !important;
                }
            }
        }

        .filter-btn {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            color: $black-color;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            min-width: 170px;
            height: 52px;
        }

    }

    .search-btn {
        color: $white-color;
        min-width: auto;
        padding: 17px 30px;
    }


    .clear-filters {
        .search-result {
            border-radius: 8px;
            background: $light-yellow;
            display: flex;
            padding: 6px 10px 6px 6px;
            align-items: center;
            gap: 10px;
            width: fit-content;
            margin-top: 10px;

            .close-btn {
                line-height: 1;
                width: 16px;
                height: 16px;
                object-fit: contain;
            }
        }


    }

    .clear-filter-btn {
        background: transparent;
        border: none;
        padding: 0px;
        color: #FF0101;
        font-size: 14px;
        font-weight: 700;
        line-height: normal;
        min-width: auto;
        white-space: nowrap;
    }

    .custom-dropdown {
        padding: 12px 16px;
        width: 100%;
        color: $black-color;
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        height: 52px;
        background: transparent;
        border: 1px solid #E7E7E7;
        border: 1px solid #E7E7E7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: auto;
        &::after {
            content: "";
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z'%3E%3C/path%3E%3C/svg%3E");
            background-size: 24px;
            width: 24px;
            height: 24px;
            margin-left: 0px;
            border: none;
            transition: transform 0.3s ease;
        }

        &.show {
            &::after {
                transform: rotate(180deg);
            }
        }

        + .dropdown-menu {
            width: 100%;
        }
    }

    .custom-interest-dropdown {        
        .custom-dropdown {
            padding: 12px 16px;
            width: 170px;
            color: $black-color;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            height: 52px;
            background: transparent;
            border: 1px solid #E7E7E7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: auto;
            border-radius: 8px;
            position: relative;
            
            &::after {
                content: "";
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z'%3E%3C/path%3E%3C/svg%3E");
                background-size: 24px;
                width: 24px;
                height: 24px;
                margin-left: 0px;
                display: inline-block;
                transition: transform 0.3s ease;
            }
            
            &.active {
                &::after {
                    transform: rotate(180deg);
                }
            }
        }
        
        .custom-interest-dropdown-menu {
            position: absolute;
            top: 77px;
            left: 0;
            width: 100vw;
            background: $white-color;
            z-index: 1050;
            overflow-y: auto;
            max-height: calc(100vh - 56px);
            border-bottom: 1px solid #ebebeb;
            display: none;
            margin-left: -10px;

            @media(max-width:575px){
                margin-left: 0px;
            }
            
            &.show {
                display: block;
            }
            
            .custom-interest-menu-content {
                width: 100%;
                margin: 0 auto;
                padding-block: 10px;
                
                .custom-interest-menu-data {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
                    gap: 15px;
                }
            }
        }
    }

    .custom-interest-menu-data {
        
        overflow-y: auto;
    }

          .interest-card {
                width: 100%;
                border-radius: 8px;
                background-color: #f0f0f0;
                cursor: pointer;
                display: flex;
                align-items: center;
                padding: 10px;
                position: relative;
                border: 1px solid #E7E7E7;
                
                &:hover {
                    background-color: #e8e8e8;
                }
          
          .interest-img {
              width: 45px;
              height: 45px;
              border-radius: 4px;
              object-fit: cover;
              margin-right: 15px;
          }
          
          .label-overlay {
              color: #333;
              font-size: 16px;
              font-weight: 500;
          }
          
          .checkmark {
              position: absolute;
              top: 50%;
              right: 15px;
              transform: translateY(-50%);
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background-color: #b936ad;
              display: none;
              align-items: center;
              justify-content: center;
              
              &::after {
                  content: "";
                  display: block;
                  width: 12px;
                  height: 8px;
                  border-left: 2px solid white;
                  border-bottom: 2px solid white;
                  transform: rotate(-45deg) translate(1px, -2px);
              }
          }
          
          &.selected {
              background-color: #f5eef5;
              border: 1px solid #b936ad;
              
              .checkmark {
                  display: flex;
              }
          }
    }

}

@media(max-width:1599px) {
    .search-bar-wrapper {
        padding-block: 8px;

        .custom-dropdown {
            height: 44px;
            width: auto;
            min-width: 134px;
            width: 100%;
        }

        .custom-interest-dropdown {
            .custom-dropdown {
                height: 44px;
                width: auto;
                min-width: 134px;
            }
        }
        .search-filters {
            .form-input-group {
                gap: 10px;

                .dropdown {
                    flex: 1;
                }

                .search-input {
                    flex: 1;
                    height: 44px;
                    padding: 8px 12px;
                    gap: 8px;
                    max-width: unset;
                }

                .form-input {
                    .select {
                        &__control {
                            height: 44px;
                            width: 140px;
                            padding: 8px 12px;
                            font-size: 13px;
                        }
                    }

                    .custom-dropdown {
                        width: 140px;
                        height: 44px;
                        padding: 8px 12px;
                        font-size: 13px;
                    }
                }

                .img-filter {
                    min-width: 140px;
                    height: 44px;
                    padding: 8px 12px;
                    font-size: 13px;
                }

                .filter-btn {
                    min-width: max-content;
                    height: 44px;
                    padding: 8px 12px;
                    font-size: 13px;
                }
            }
        }

        .search-btn {
            padding: 12px 18px;
            height: 44px;
            font-size: 14px;
            span {
                font-size: 13px;
            }
            svg {
                width: 14px;
                height: 14px;
            }
        }
    }
}

@media(max-width:575px) {
    .search-bar-wrapper {
        .search-filters {
            flex-direction: column;

            .form-input-group {
                .search-input {
                    max-width: 100%;
                }

                .form-input {
                    width: 100%;

                    .custom-dropdown {
                        width: 100%;
                    }
                }

                .img-filter {
                    min-width: 100%;
                }

                .filter-btn {
                    min-width: 100%;
                }

                .custom-interest-dropdown {
                    .custom-interest-dropdown-menu {
                        max-height: 100dvh;
                        padding: 10px;
                    }
                }
                
                .custom-interest-menu-data {
                    .flex-1 {
                        flex: 1;
                    }
                }
                
                .interest-card {
                    height: 80px;
                    
                  
                    
                    .label-overlay {
                        font-size: 14px;
                    }
                }
            }
        }

        .filter-result-dispaly {
            flex-direction: column;
        }

        .clear-filter-btn {
            text-align: left;
        }

        .search-btn {
            min-width: 100%;
            justify-content: center;
        }
    }
}
@media(min-width:576px) and (max-width:991px){
     .search-bar-wrapper {
        .search-filters {
            flex-direction: column;

            .form-input-group {
                .search-input {
                    max-width: 100%;
                }

                .form-input {
                    width: 48%;

                    .custom-dropdown {
                        width: 100%;
                    }
                }

                .img-filter {
                    min-width: 48%;
                }

                .filter-btn {
                    min-width: 48%;
                }

                .custom-interest-dropdown {
                    .custom-interest-dropdown-menu {
                        max-height: calc(100vh - 65px);
                        
                        .custom-interest-menu-content {
                            padding-block: 15px;
                            
                            .custom-interest-menu-data {
                                gap: 10px;
                            }
                        }
                    }
                }
                
            }
        }

        .filter-result-dispaly {
            flex-direction: column;
        }

        .clear-filter-btn {
            text-align: left;
        }

        .search-btn {
            min-width: 100%;
            justify-content: center;
        }
    }
}