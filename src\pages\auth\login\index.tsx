import { useLoginMutation } from "@/api";
import Pass<PERSON><PERSON>ield from "@/components/common/PasswordField";
import {
  stringRequiredValidation
} from "@/formSchema/schemaValidations";
import { useTranslation } from "@/hooks/useTranslation";
import { ROUTE_PATH } from "@/routes";
import useUserStore, { setAuthLoading } from "@/stores/user";
import useChatStore from "@/stores/useChatStore";
import { LoginInterface } from "@/types/auth";
import { useFormik } from "formik";
import parse from "html-react-parser";
import React from "react";
import { Button, Form } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import * as Yup from "yup";

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { setUserInfo, setRememberMeInfo } = useUserStore();
  const { initializeSocket } = useChatStore();
  const { mutateAsync: login, isPending } = useLoginMutation();
  const rememberMeEmail = useUserStore(
    (state) => state.rememberMeInfo?.email || ""
  );

  const formik = useFormik<LoginInterface & { rememberMe: boolean }>({
    initialValues: {
      email: rememberMeEmail,
      password: "",
      rememberMe: !!rememberMeEmail,
    },
    validationSchema: Yup.object({
      email: stringRequiredValidation(t("login.emailOrUsername")),
      password: stringRequiredValidation(t("login.password")),
      rememberMe: Yup.boolean(),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const response = await login({
          email: values.email,
          password: values.password,
          role: "customer",
        });
        if (response?.data?.access_token && response?.data?.user?.id) {
          setAuthLoading(true);
          setUserInfo(response.data);

          // Initialize socket connection after successful login
          setTimeout(() => {
            initializeSocket();
          }, 500);

          if (values.rememberMe) {
            setRememberMeInfo({ email: values.email });
          } else {
            setRememberMeInfo({ email: "" });
          }
          navigate(ROUTE_PATH.HOME);
        }
      } catch (err: unknown) {
        console.log(err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    touched,
    errors,
    isSubmitting,
    submitCount,
  } = formik;

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4">
        <div className="auth-form-heading text-center">
          <h3 className="mb-2 mb-sm-3">{parse(t("login.heading"))}</h3>
        </div>
        <Form noValidate onSubmit={handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <Form.Group className="form-input" controlId="email">
              <Form.Label>{t("login.emailOrUsername")}</Form.Label>
              <Form.Control
                name="email"
                placeholder={t("login.emailPlaceholder")}
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                isInvalid={!!touched.email && !!errors.email}
              />
              <Form.Control.Feedback type="invalid">
                {errors.email}
              </Form.Control.Feedback>
            </Form.Group>
            <PasswordField
              label={t("login.password")}
              name="password"
              placeholder={t("login.passwordPlaceholder")}
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
              submitCount={submitCount}
            />
          </div>
          <div className="d-flex align-items-center justify-content-between text-end mt-2 mt-sm-3">
            <Form.Check type="checkbox" id={`check-api-checkbox`}>
              <Form.Check.Input
                type="checkbox"
                name="rememberMe"
                checked={values.rememberMe}
                onChange={handleChange}
                onBlur={handleBlur}
              />
              <Form.Check.Label>{t("login.rememberMe")}</Form.Check.Label>
            </Form.Check>
            <Link to={ROUTE_PATH.FORGOTPASSWORD} className="forgot-link">
              {t("login.forgotPassword")}
            </Link>
          </div>
          <Button
            type="submit"
            className="w-100 mt-3 mt-sm-4"
            disabled={isSubmitting || isPending}
          >
            {isPending ? t("login.loggingIn") : t("login.submit")}
          </Button>
        </Form>
      </div>
      <div className="text-center mt-3 mt-sm-4">
        <p className="mb-0">
          {t("login.noAccount")}{" "}
          <Link to={ROUTE_PATH.SIGNUP}>{t("login.signup")}</Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
