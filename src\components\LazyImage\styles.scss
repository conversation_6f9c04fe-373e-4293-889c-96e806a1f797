.lazy-image-wrapper {
  height: 220px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
  position: relative;
  background-color: #f8f9fa;
}

.image-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8px 8px 0 0;
  z-index: 1;
}

.placeholder-blur {
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  z-index: 2;
  transition: opacity 0.3s ease-out;
}

.member-img {
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  z-index: 3;

  &.transition-opacity {
    transition: opacity 0.4s ease-out;
  }

  &.opacity-0 {
    opacity: 0;
  }

  &.opacity-100 {
    opacity: 1;
  }

  &.error {
    opacity: 0;
  }
}

.error-state {
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
  z-index: 4;
}

// Animations
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    background-color: #eee;
  }
  50% {
    background-color: #ddd;
  }
  100% {
    background-color: #eee;
  }
}

// Hover effects for better UX
.lazy-image-wrapper:hover .member-img {
  transform: scale(1.02);
  transition: transform 0.3s ease-out, opacity 0.4s ease-out;
}

// Responsive optimizations
@media (max-width: 768px) {
  .lazy-image-wrapper {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .lazy-image-wrapper {
    height: 180px;
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .member-img,
  .placeholder-blur,
  .lazy-image-wrapper:hover .member-img {
    transition: none !important;
    transform: none !important;
  }

  .image-skeleton {
    animation: none;
    background: #f0f0f0;
  }
}
