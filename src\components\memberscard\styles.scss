@use '@/variables' as *;

.member-card {
    border-radius: 10px;
    border: 1px solid $light-gray-color;
    background: $white-color;
    width: 100%;
    max-width: 350px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        border-color: rgba(185, 54, 173, 0.2);

        .member-img {
            transform: scale(1.02);
        }

        .favorite-icon {
            transform: scale(1.1);
        }
    }

    @media (max-width: 1200px) {
        max-width: 500px;
    }

    @media (max-width: 768px) {
        max-width: 100%;

        &:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
    }

    

    .member-img {
        height: 220px;
        object-fit: cover;
        border-radius: 8px 8px 0px 0px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform;
    }

    .favorite-icon {
        position: absolute;
        width: 40px;
        height: 40px;
        min-width: 40px;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
        background: $white-color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 20px;
        right: 20px;
        transition: all 0.2s ease;
        will-change: transform;

        &:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
        }
    }

    .name {
        color: $black-color;
        font-size: 18px;
        font-weight: 700;
        line-height: 1.4;
        letter-spacing: 0.36px;
    }

    .location {
        color: $link-color;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
        letter-spacing: 0.28px;
    }

    .email-btn {
        border-radius: 9.6px;
        border: 1.2px solid $light-gray-color;
        background: $white-color;
        padding: 10px;
        width: 52px;
        min-width: 52px;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn {
        color: $black-color;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        border-radius: 8px;
        padding: 7px 10px;
        min-width: auto;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        &:hover::before {
            left: 100%;
        }

        &.first-btn {
            background: $light-yellow;

            &:hover {
                background: lighten($light-yellow, 5%);
                transform: translateY(-1px);
            }
        }

        &.second-btn {
            background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);
            color: $white-color;

            &:hover {
                background: linear-gradient(270deg, lighten(#B936AD, 5%) 0%, lighten(#79259C, 5%) 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(185, 54, 173, 0.3);
            }
        }
    }
}

.placeholder-image {
    padding: 10px;
    box-sizing: border-box;
    .member-img {
        object-fit: contain;
        height: 100%;
    }
}

.user-profile-detail {
    width: 32rem !important;

    &-header {
        padding: 24px;
        border: 1px solid $light-gray-color;

        .name {
            color: $black-color;
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .location {
            color: $link-color;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.4;
            letter-spacing: 0.28px;
        }

        .btn-close {
            opacity: 1;

            &:focus {
                box-shadow: none;
            }
        }
    }

    .offcanvas-body {
        .img-section {
            .large-img {
                max-height: 300px;
                object-fit: contain;
                border-radius: 8px;
            }

            .favorite-icon {
                position: absolute;
                width: 40px;
                height: 40px;
                min-width: 40px;
                box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
                background: $white-color;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 20px;
                right: 20px;
            }

            .small-img {
                height: 110px;
                max-width: 110px;
                object-fit: cover;
                border-radius: 8px;
            }
        }

        .btn-section {
            gap: 12px;

            .btn {
                color: $black-color;
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
                border-radius: 8px;
                padding: 15px 10px;
                min-width: auto;

                &.first-btn {
                    background: $light-yellow;
                }

                &.second-btn {
                    background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);
                    color: $white-color;
                }
            }

            .email-btn {
                border-radius: 9.6px;
                border: 1.2px solid $light-gray-color;
                background: $white-color;
                padding: 10px;
                width: 52px;
                min-width: 52px;
                height: 52px;
                display: flex;
                align-items: center;
                justify-content: center;
            }


        }

        .details-section {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px;

            .title {
                color: $black-color;
                font-size: 14px;
                font-weight: 700;
                line-height: normal;
                border-bottom: 1px solid $light-gray-color;
                padding-bottom: 8px;
                margin-bottom: 8px;
            }

            .description {
                color: $link-color;
                font-size: 14px;
                font-weight: 400;
                line-height: 1.4;
            }

            &-info {
                color: $link-color;
                font-size: 13px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 8px;

                span {
                    color: $black-color;
                }
            }
        }
    }
}

@media(max-width:575px) {
    .member-card {
        max-width: 100%;
        min-width: 100%;
    }
    .user-profile-detail {
        .image-scroll{
            overflow: hidden;
            overflow-x: auto;
            padding-bottom: 10px;
        }
    }
}


// Image modal related styles
.cursor-pointer {
    cursor: pointer !important;
}

// Image hover effects for better UX
.large-img:hover,
.small-img:hover {
    opacity: 0.9;
    transition: opacity 0.2s ease;
}

.image-scroll {
    overflow-x: auto;
    padding-bottom: 10px;
    user-select: none;

    &::-webkit-scrollbar {
        display: none;
    }
}