@use '@/variables' as *;

.member-card {
    border-radius: 10px;
    border: 1px solid $light-gray-color;
    background: $white-color;
    width: 100%;
    max-width: 350px;
    cursor: pointer;
    @media (max-width: 1200px) {
        max-width: 500px;
    }

    @media (max-width: 768px) {
        max-width: 100%;
    }

    

    .member-img {
        height: 220px;
        object-fit: cover;
        border-radius: 8px 8px 0px 0px;
    }

    .favorite-icon {
        position: absolute;
        width: 40px;
        height: 40px;
        min-width: 40px;
        box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
        background: $white-color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 20px;
        right: 20px;
    }

    .name {
        color: $black-color;
        font-size: 18px;
        font-weight: 700;
        line-height: 1.4;
        letter-spacing: 0.36px;
    }

    .location {
        color: $link-color;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.4;
        letter-spacing: 0.28px;
    }

    .email-btn {
        border-radius: 9.6px;
        border: 1.2px solid $light-gray-color;
        background: $white-color;
        padding: 10px;
        width: 52px;
        min-width: 52px;
        height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn {
        color: $black-color;
        font-size: 16px;
        font-weight: 500;
        line-height: normal;
        border-radius: 8px;
        padding: 7px 10px;
        min-width: auto;

        &.first-btn {
            background: $light-yellow;
        }

        &.second-btn {
            background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);
            color: $white-color;
        }
    }
}

.placeholder-image {
    padding: 10px;
    box-sizing: border-box;
    .member-img {
        object-fit: contain;
        height: 100%;
    }
}

.user-profile-detail {
    width: 32rem !important;

    &-header {
        padding: 24px;
        border: 1px solid $light-gray-color;

        .name {
            color: $black-color;
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .location {
            color: $link-color;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.4;
            letter-spacing: 0.28px;
        }

        .btn-close {
            opacity: 1;

            &:focus {
                box-shadow: none;
            }
        }
    }

    .offcanvas-body {
        .img-section {
            .large-img {
                max-height: 300px;
                object-fit: contain;
                border-radius: 8px;
            }

            .favorite-icon {
                position: absolute;
                width: 40px;
                height: 40px;
                min-width: 40px;
                box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.08);
                background: $white-color;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                top: 20px;
                right: 20px;
            }

            .small-img {
                height: 110px;
                max-width: 110px;
                object-fit: cover;
                border-radius: 8px;
            }
        }

        .btn-section {
            gap: 12px;

            .btn {
                color: $black-color;
                font-size: 16px;
                font-weight: 500;
                line-height: normal;
                border-radius: 8px;
                padding: 15px 10px;
                min-width: auto;

                &.first-btn {
                    background: $light-yellow;
                }

                &.second-btn {
                    background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);
                    color: $white-color;
                }
            }

            .email-btn {
                border-radius: 9.6px;
                border: 1.2px solid $light-gray-color;
                background: $white-color;
                padding: 10px;
                width: 52px;
                min-width: 52px;
                height: 52px;
                display: flex;
                align-items: center;
                justify-content: center;
            }


        }

        .details-section {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px;

            .title {
                color: $black-color;
                font-size: 14px;
                font-weight: 700;
                line-height: normal;
                border-bottom: 1px solid $light-gray-color;
                padding-bottom: 8px;
                margin-bottom: 8px;
            }

            .description {
                color: $link-color;
                font-size: 14px;
                font-weight: 400;
                line-height: 1.4;
            }

            &-info {
                color: $link-color;
                font-size: 13px;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 8px;

                span {
                    color: $black-color;
                }
            }
        }
    }
}

@media(max-width:575px) {
    .member-card {
        max-width: 100%;
        min-width: 100%;
    }
    .user-profile-detail {
        .image-scroll{
            overflow: hidden;
            overflow-x: auto;
            padding-bottom: 10px;
        }
    }
}


// Image modal related styles
.cursor-pointer {
    cursor: pointer !important;
}

// Image hover effects for better UX
.large-img:hover,
.small-img:hover {
    opacity: 0.9;
    transition: opacity 0.2s ease;
}

.image-scroll {
    overflow-x: auto;
    padding-bottom: 10px;
    user-select: none;

    &::-webkit-scrollbar {
        display: none;
    }
}