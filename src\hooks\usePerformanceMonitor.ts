import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  imageLoadTime: number;
  scrollPerformance: number;
  memoryUsage?: number;
}

export const usePerformanceMonitor = (componentName: string, enabled = false) => {
  const renderStartTime = useRef<number>(Date.now());
  const metricsRef = useRef<PerformanceMetrics>({
    renderTime: 0,
    imageLoadTime: 0,
    scrollPerformance: 0,
  });

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    // Measure render time
    const renderEndTime = Date.now();
    metricsRef.current.renderTime = renderEndTime - renderStartTime.current;

    // Monitor memory usage if available
    if ('memory' in performance) {
      metricsRef.current.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚀 Performance Metrics - ${componentName}`);
      console.log(`Render Time: ${metricsRef.current.renderTime}ms`);
      if (metricsRef.current.memoryUsage) {
        console.log(`Memory Usage: ${(metricsRef.current.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      }
      console.groupEnd();
    }
  }, [componentName, enabled]);

  const measureImageLoad = (startTime: number) => {
    const loadTime = Date.now() - startTime;
    metricsRef.current.imageLoadTime = loadTime;
    
    if (enabled && process.env.NODE_ENV === 'development') {
      console.log(`📸 Image Load Time: ${loadTime}ms`);
    }
    
    return loadTime;
  };

  const measureScrollPerformance = () => {
    if (!enabled) return () => {};

    const startTime = Date.now();
    
    return () => {
      const scrollTime = Date.now() - startTime;
      metricsRef.current.scrollPerformance = scrollTime;
      
      if (process.env.NODE_ENV === 'development' && scrollTime > 16) {
        console.warn(`⚠️ Slow scroll detected: ${scrollTime}ms (target: <16ms)`);
      }
    };
  };

  return {
    metrics: metricsRef.current,
    measureImageLoad,
    measureScrollPerformance,
  };
};

// Web Vitals monitoring
export const useWebVitals = (enabled = false) => {
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    // Monitor Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 LCP: ${lastEntry.startTime.toFixed(2)}ms`);
      }
    });

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      // LCP not supported
    }

    // Monitor Cumulative Layout Shift (CLS)
    let clsValue = 0;
    const clsObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 CLS: ${clsValue.toFixed(4)}`);
      }
    });

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (e) {
      // CLS not supported
    }

    return () => {
      observer.disconnect();
      clsObserver.disconnect();
    };
  }, [enabled]);
};
