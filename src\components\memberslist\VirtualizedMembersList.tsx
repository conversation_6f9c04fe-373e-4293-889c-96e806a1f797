import React, { useMemo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import MembersCard, { MembersCardSkeleton } from '../memberscard/MembersCard';
import { ModelInterface } from '@/types';
import { Spinner } from 'react-bootstrap';

interface VirtualizedMembersListProps {
  models: ModelInterface[];
  hasNextPage: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  containerWidth: number;
  containerHeight: number;
}

const VirtualizedMembersList: React.FC<VirtualizedMembersListProps> = ({
  models,
  hasNextPage,
  isLoading,
  isFetchingNextPage,
  fetchNextPage,
  containerWidth,
  containerHeight,
}) => {
  // Calculate grid dimensions
  const cardWidth = 272; // From SCSS
  const cardHeight = 350; // Approximate height including padding
  const gap = 16; // Gap between cards
  
  const columnsCount = Math.floor((containerWidth + gap) / (cardWidth + gap));
  const rowsCount = Math.ceil(models.length / columnsCount);
  
  // Calculate if we need to show skeleton rows
  const skeletonRowsCount = isLoading ? Math.ceil(10 / columnsCount) : 0; // Show 10 skeleton cards
  const totalRowsCount = rowsCount + skeletonRowsCount + (isFetchingNextPage ? 1 : 0);

  // Check if item is loaded
  const isItemLoaded = (index: number) => {
    const totalItems = models.length;
    const itemIndex = index * columnsCount;
    return itemIndex < totalItems;
  };

  // Load more items
  const loadMoreItems = hasNextPage && !isFetchingNextPage ? fetchNextPage : () => {};

  // Item count for infinite loader
  const itemCount = hasNextPage ? totalRowsCount + 1 : totalRowsCount;

  // Cell renderer
  const Cell = ({ columnIndex, rowIndex, style }: any) => {
    const itemIndex = rowIndex * columnsCount + columnIndex;
    
    // Show loading spinner row at the bottom
    if (isFetchingNextPage && rowIndex === totalRowsCount - 1) {
      if (columnIndex === Math.floor(columnsCount / 2)) {
        return (
          <div style={style} className="d-flex justify-content-center align-items-center">
            <Spinner animation="border" />
          </div>
        );
      }
      return <div style={style} />;
    }
    
    // Show skeleton cards during initial loading
    if (isLoading && itemIndex >= models.length) {
      return (
        <div style={{ ...style, padding: gap / 2 }}>
          <MembersCardSkeleton />
        </div>
      );
    }
    
    // Show actual member card
    const model = models[itemIndex];
    if (model) {
      return (
        <div style={{ ...style, padding: gap / 2 }}>
          <MembersCard key={model.id} {...model} />
        </div>
      );
    }
    
    // Empty cell
    return <div style={style} />;
  };

  const memoizedCell = useMemo(() => Cell, [models, isLoading, isFetchingNextPage, columnsCount, gap]);

  if (isLoading && models.length === 0) {
    // Show skeleton grid for initial loading
    return (
      <div className="d-flex flex-wrap members-list-content">
        {Array.from({ length: 10 }).map((_, index) => (
          <div key={index} style={{ padding: gap / 2 }}>
            <MembersCardSkeleton />
          </div>
        ))}
      </div>
    );
  }

  return (
    <InfiniteLoader
      isItemLoaded={isItemLoaded}
      itemCount={itemCount}
      loadMoreItems={loadMoreItems}
    >
      {({ onItemsRendered, ref }) => (
        <Grid
          ref={ref}
          columnCount={columnsCount}
          columnWidth={cardWidth + gap}
          height={containerHeight}
          rowCount={totalRowsCount}
          rowHeight={cardHeight + gap}
          width={containerWidth}
          onItemsRendered={({
            visibleRowStartIndex,
            visibleRowStopIndex,
            visibleColumnStartIndex,
            visibleColumnStopIndex,
          }) => {
            onItemsRendered({
              overscanStartIndex: visibleRowStartIndex * columnsCount + visibleColumnStartIndex,
              overscanStopIndex: visibleRowStopIndex * columnsCount + visibleColumnStopIndex,
              visibleStartIndex: visibleRowStartIndex * columnsCount + visibleColumnStartIndex,
              visibleStopIndex: visibleRowStopIndex * columnsCount + visibleColumnStopIndex,
            });
          }}
        >
          {memoizedCell}
        </Grid>
      )}
    </InfiniteLoader>
  );
};

export default VirtualizedMembersList;
