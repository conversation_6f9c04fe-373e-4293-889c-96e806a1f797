import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import "./styles.scss";
import useChatStore from "@/stores/useChatStore";
import {
  Button,
  Form,
  InputGroup,
  Card,
  CloseButton,
  Image,
} from "react-bootstrap";
import { FormatS3ImgUrl } from "@/utils";
import defaultProfile from "@/assets/images/user.png";
import { AttachSquare, Send, Gift, Happyemoji } from "iconsax-react";

const ChatPopup = () => {
  const {
    activeChatUser,
    setActiveChatUser,
    messages,
    sendMessage,
    isConnected,
    getChatMessages,
  } = useChatStore();
  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);

  const currentMessages = useMemo(() => {
    return activeChatUser ? messages[activeChatUser.id] || [] : [];
  }, [activeChatUser, messages]);

  const userAvatarUrl = useMemo(() => {
    return activeChatUser?.avatar
      ? FormatS3ImgUrl(activeChatUser.avatar)
      : defaultProfile;
  }, [activeChatUser?.avatar]);

  useEffect(() => {
    if (activeChatUser && isConnected) {
      getChatMessages(activeChatUser.id);
    }
  }, [activeChatUser, isConnected, getChatMessages]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [currentMessages]);

  // Keep placeholder visibility in sync with the current input value
  useEffect(() => {
    const editableElement = inputRef.current;
    if (!editableElement) return;
    if (input.trim().length > 0) {
      editableElement.setAttribute("data-has-value", "true");
    } else {
      editableElement.removeAttribute("data-has-value");
    }
  }, [input]);

  const handleSend = useCallback(() => {
    if (input.trim() && isConnected && activeChatUser) {
      sendMessage(activeChatUser.id, input);
      setInput("");
      // Clear the contentEditable element without disrupting caret handling
      if (inputRef.current) {
        inputRef.current.textContent = "";
        inputRef.current.focus();
      }
    }
  }, [input, isConnected, activeChatUser, sendMessage]);

  const handleClose = useCallback(() => {
    setActiveChatUser(null);
  }, [setActiveChatUser]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  const groupedMessages = useMemo(() => {
    const groups: Record<string, typeof currentMessages> = {};

    currentMessages.forEach((msg) => {
      const date = new Date(msg.timestamp).toISOString().split("T")[0];

      if (!groups[date]) groups[date] = [];
      groups[date].push(msg);
    });

    return groups;
  }, [currentMessages]);

  const getDateLabel = (dateString: string) => {
    const today = new Date();
    const msgDate = new Date(dateString);
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    if (msgDate.toDateString() === today.toDateString()) return "Today";
    if (msgDate.toDateString() === yesterday.toDateString()) return "Yesterday";

    return msgDate.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  };

  if (!activeChatUser) return null;

  return (
    <Card className="position-fixed chatpopup">
      <div className="d-flex justify-content-between align-items-center chatpopup-header">
        <div className="d-flex gap-2 align-items-center">
          <Image
            src={userAvatarUrl}
            alt={activeChatUser.name}
            className="user-img object-fit-cover"
          />
          <div className="d-flex flex-column gap-1">
            <h6 className="name mb-0">{activeChatUser.name}</h6>
            <p className="location mb-0">{activeChatUser.location}</p>
            {/* <div
              className={`connection-status small ${isConnected ? "text-success" : "text-danger"}`}
            >
              {isConnected ? "● Online" : "● Offline"}
            </div> */}
          </div>
        </div>
        <CloseButton
          variant="black"
          className="close-btn"
          onClick={handleClose}
        />
      </div>
      <div className="message-screen" style={{ flex: 1 }}>
        {Object.entries(groupedMessages).map(([date, msgs]) => (
          <div key={date}>
            <div className="text-center text-muted small my-2">
              {getDateLabel(date)}
            </div>
            {msgs.map((msg, idx) => (
              <div
                key={msg.id || idx}
                className={`message ${msg.sender === "me" ? "message-send ms-auto text-start" : "message-receive text-start me-auto"}`}
              >
                {msg.message}
                {msg.timestamp && (
                  <div
                    className={`message-timestamp small text-end ${msg.sender === "me" ? "text-light" : "text-muted"}`}
                  >
                    {new Date(msg.timestamp).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <div className="chatpopup-footer">
        <Form
          onSubmit={(e) => {
            e.preventDefault();
            handleSend();
          }}
        >
          <InputGroup className="gap-3 align-items-center">
            {/* <Form.Control
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter Message"
            /> */}

            <div
              ref={inputRef}
              contentEditable
              suppressContentEditableWarning
              role="textbox"
              aria-multiline="true"
              onInput={(e) => setInput(e.currentTarget.textContent || "")}
              onKeyDown={handleKeyDown}
              className="form-control"
              data-placeholder="Enter Message"
              style={{
                height: "max-content",
                maxHeight: "57px",
                overflowY: "auto",
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                outline: "none",
              }}
            />
            <div className="d-flex align-items-center gap-3">
              <Button className="icons-btn">
                <AttachSquare size="24" color="#000000" />
              </Button>
              <Button className="icons-btn">
                <Gift size="24" color="#000000" variant="Outline" />
              </Button>
              <Button className="icons-btn">
                <Happyemoji size="24" color="#000000" variant="Outline" />
              </Button>
              <Button
                variant="primary"
                className="send-btn"
                onClick={handleSend}
                disabled={!isConnected || !input.trim()}
                title={!isConnected ? "Connecting..." : "Send message"}
              >
                <Send size="24" color="#ffffff" variant="Bold" />
              </Button>
            </div>
          </InputGroup>
        </Form>
      </div>
    </Card>
  );
};

export default ChatPopup;
