import { FormatS3ImgUrl } from "@/utils";
import { ArrowLeft2, ArrowRight2 } from "iconsax-react";
import React, { useState } from "react";
import { Button, Image, Modal } from "react-bootstrap";
import "./styles.scss";

interface ImageModalProps {
  show: boolean;
  onHide: () => void;
  images: string[]; // Array of image URLs/paths
  initialIndex?: number; // Starting image index
  title?: string;
}

const ImageModal: React.FC<ImageModalProps> = ({
  show,
  onHide,
  images = [],
  initialIndex = 0,
  title,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Reset index when modal opens
  React.useEffect(() => {
    if (show) {
      setCurrentIndex(initialIndex);
    }
  }, [show, initialIndex]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowLeft") {
      handlePrevious();
    } else if (e.key === "ArrowRight") {
      handleNext();
    } else if (e.key === "Escape") {
      onHide();
    }
  };

  if (!images.length) return null;

  const currentImage = images[currentIndex];
  const hasMultipleImages = images.length > 1;

  return (
    <Modal
      show={show}
      onHide={onHide}
      size="xl"
      centered
      className="image-modal"
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <Modal.Header className="border-0 pb-0 shrink-0" closeButton>
        <Modal.Title className="text-center w-100">
          {title && <span>{title}</span>}
        </Modal.Title>
      </Modal.Header>

      <Modal.Body className="p-0 position-relative my-2 flex-grow-1">
        <div className="image-container">
          <Image
            src={FormatS3ImgUrl(currentImage)}
            alt={`Image ${currentIndex + 1}`}
            className="modal-image"
            fluid
          />
        </div>
        {hasMultipleImages && (
        <>
          <Button
            variant="link"
            className="carousel-btn carousel-btn-prev"
            onClick={handlePrevious}
            aria-label="Previous image"
          >
            <div className="button-icon">
              <ArrowLeft2 size="18" color="#000" />
            </div>
          </Button>

          <Button
            variant="link"
            className="carousel-btn carousel-btn-next"
            onClick={handleNext}
            aria-label="Next image"
          >
            <div className="button-icon">
              <ArrowRight2 size="18" color="#000" />
            </div>
          </Button>
        </>
      )}
      </Modal.Body>

      

      {hasMultipleImages && (
        <Modal.Footer className="border-0 pt-0 d-block image-modal-scroller shrink-0">
          <div className="d-flex gap-2 justify-content-center w-max-content">
            {images.map((img, index) => (
              <div
                key={index}
                className={`thumbnail ${
                  index === currentIndex ? "active" : ""
                }`}
                onClick={() => setCurrentIndex(index)}
              >
                <Image
                  src={FormatS3ImgUrl(img)}
                  alt={`Thumbnail ${index + 1}`}
                  className="thumbnail-image"
                />
              </div>
            ))}
          </div>
        </Modal.Footer>
      )}
    </Modal>
  );
};

export default ImageModal;
